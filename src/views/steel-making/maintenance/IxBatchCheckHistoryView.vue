<template>
  <div class="ix-batch-check-history">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>IX批次检查历史</h3>
        </div>
      </template>

      <!-- 查询表单 -->
      <el-form :model="queryForm" inline class="query-form">
        <el-form-item label="任务状态">
          <el-select v-model="queryForm.status" placeholder="请选择状态" clearable>
            <el-option label="待执行" value="pending" />
            <el-option label="执行中" value="running" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column prop="id" label="任务ID" width="80" />
        <el-table-column prop="task_name" label="任务名称" min-width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="result_status" label="结果状态" width="100">
          <template #default="scope">
            <el-tag
              :type="getResultStatusTagType(scope.row.result_status)"
              size="small"
            >
              {{ scope.row.result_status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="error_count" label="错误记录数" width="120" />
        <el-table-column prop="created_at" label="创建时间" width="180" />
        <el-table-column prop="completed_at" label="完成时间" width="180" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="viewDetails(scope.row)"
              :disabled="!scope.row.check_result"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryForm.page"
          v-model:page-size="queryForm.page_size"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchData"
          @current-change="fetchData"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="IX批次检查详情"
      width="80%"
      :close-on-click-modal="false"
    >
      <div v-if="selectedTask">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务名称">{{ selectedTask.task_name }}</el-descriptions-item>
          <el-descriptions-item label="检查时间">{{ selectedTask.check_result?.summary?.check_time }}</el-descriptions-item>
          <el-descriptions-item label="错误记录数">{{ selectedTask.check_result?.error_count }}</el-descriptions-item>
          <el-descriptions-item label="系统">{{ selectedTask.check_result?.summary?.system }}</el-descriptions-item>
        </el-descriptions>

        <div v-if="selectedTask.check_result?.error_records?.length > 0" style="margin-top: 20px;">
          <h4>错误记录详情</h4>
          <el-table :data="selectedTask.check_result.error_records" border>
            <el-table-column prop="batchno" label="批次号" width="150" />
            <el-table-column prop="sysid" label="系统ID" width="100" />
            <el-table-column prop="startdate" label="开始日期" width="120" />
            <el-table-column prop="starttime" label="开始时间" width="120" />
            <el-table-column prop="execempno" label="执行员工号" width="120" />
            <el-table-column prop="execresult" label="执行结果" width="100">
              <template #default="scope">
                <el-tag type="danger">{{ scope.row.execresult }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="execmsg" label="错误信息" min-width="200" show-overflow-tooltip />
          </el-table>
        </div>
        
        <div v-else style="margin-top: 20px; text-align: center; color: #909399;">
          <el-empty description="未发现错误记录" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { maintenanceHistoryApi } from '@/api'

// 接口定义
interface TaskHistoryQuery {
  status?: string
  start_date?: string
  end_date?: string
  page: number
  page_size: number
}

interface IxBatchCheckTask {
  id: number
  task_name: string
  status: string
  result_status: string
  error_message?: string
  check_result?: any
  created_at: string
  started_at?: string
  completed_at?: string
  created_by?: number
  created_by_name?: string
  duration?: number
}

// 响应式数据
const loading = ref(false)
const tableData = ref<IxBatchCheckTask[]>([])
const total = ref(0)
const dateRange = ref<[string, string] | null>(null)
const detailDialogVisible = ref(false)
const selectedTask = ref<IxBatchCheckTask | null>(null)

// 查询表单
const queryForm = reactive<TaskHistoryQuery>({
  status: '',
  page: 1,
  page_size: 20
})

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params: TaskHistoryQuery = {
      ...queryForm
    }
    
    if (dateRange.value) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    
    const response = await maintenanceHistoryApi.getIxBatchCheckHistory(params)
    
    if (response.code === 200) {
      tableData.value = response.data.items
      total.value = response.data.total
    } else {
      ElMessage.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryForm.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  queryForm.status = ''
  queryForm.page = 1
  dateRange.value = null
  fetchData()
}

// 查看详情
const viewDetails = (task: IxBatchCheckTask) => {
  selectedTask.value = task
  detailDialogVisible.value = true
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待执行',
    running: '执行中',
    completed: '已完成',
    failed: '失败'
  }
  return statusMap[status] || status
}

// 获取结果状态标签类型
const getResultStatusTagType = (resultStatus: string) => {
  return resultStatus === '有异常' ? 'danger' : 'success'
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.ix-batch-check-history {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.query-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
