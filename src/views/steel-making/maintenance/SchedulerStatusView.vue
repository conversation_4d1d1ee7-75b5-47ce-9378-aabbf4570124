<template>
  <div class="scheduler-status-container">
    <div class="page-header">
      <h2>定时任务状态</h2>
      <p class="page-description">查看系统定时任务的运行状态和下次执行时间</p>
    </div>

    <!-- 调度器状态卡片 -->
    <el-card class="status-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>调度器状态</span>
          <el-button type="primary" size="small" @click="refreshStatus">刷新状态</el-button>
        </div>
      </template>
      
      <div v-loading="loading" class="status-content">
        <div v-if="schedulerStatus" class="status-info">
          <el-row :gutter="24">
            <el-col :span="6">
              <div class="status-item">
                <div class="status-label">调度器状态</div>
                <div class="status-value">
                  <el-tag :type="schedulerStatus.status === 'running' ? 'success' : 'danger'" size="large">
                    {{ schedulerStatus.status === 'running' ? '运行中' : '已停止' }}
                  </el-tag>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="status-item">
                <div class="status-label">任务数量</div>
                <div class="status-value">{{ schedulerStatus.jobs?.length || 0 }} 个</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="status-item">
                <div class="status-label">最后更新时间</div>
                <div class="status-value">{{ formatDateTime(new Date()) }}</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>

    <!-- 任务列表 -->
    <el-card class="jobs-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>定时任务列表</span>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="schedulerStatus?.jobs || []"
        style="width: 100%"
        stripe
        border
      >
        <el-table-column prop="id" label="任务ID" width="200" />
        <el-table-column prop="name" label="任务名称" min-width="150" />
        <el-table-column prop="trigger" label="触发器" min-width="200" show-overflow-tooltip />
        <el-table-column prop="next_run_time" label="下次执行时间" width="180">
          <template #default="scope">
            {{ scope.row.next_run_time ? formatDateTime(scope.row.next_run_time) : '未设置' }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag type="success" size="small">活跃</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 任务说明 -->
    <el-card class="description-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>任务说明</span>
        </div>
      </template>
      
      <div class="description-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="物料消耗对比">
            每2小时自动执行一次，对比ERP与MES的每日物料消耗数据差异
          </el-descriptions-item>
          <el-descriptions-item label="ERP电文检查">
            每30分钟自动执行一次，检查ERP系统的接收和发送电文错误
          </el-descriptions-item>
          <el-descriptions-item label="MES电文检查">
            每30分钟自动执行一次，检查MES系统的接收和发送电文错误
          </el-descriptions-item>
           <el-descriptions-item label="IX收账作业检查">
            每30分钟自动执行一次，检查 IX 是否发生错误，导致无法进行收数
          </el-descriptions-item>
           <el-descriptions-item label="炼钢成本数据核对">
            每 2 小时自动执行一次，检查 OJ、MR、IP 的物料消耗数据是否一致
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { maintenanceHistoryApi, type SchedulerStatus } from '@/api'

// 响应式数据
const loading = ref(false)
const schedulerStatus = ref<SchedulerStatus | null>(null)

// 获取调度器状态
const fetchSchedulerStatus = async () => {
  loading.value = true
  try {
    const response = await maintenanceHistoryApi.getSchedulerStatus()
    
    if (response.code === 200) {
      schedulerStatus.value = response.data
    } else {
      ElMessage.error(response.msg || '获取调度器状态失败')
    }
  } catch (error) {
    console.error('获取调度器状态失败:', error)
    ElMessage.error('获取调度器状态失败')
  } finally {
    loading.value = false
  }
}

// 刷新状态
const refreshStatus = () => {
  fetchSchedulerStatus()
}

// 格式化日期时间
const formatDateTime = (dateTime: string | Date) => {
  if (typeof dateTime === 'string') {
    return new Date(dateTime).toLocaleString('zh-CN')
  }
  return dateTime.toLocaleString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchSchedulerStatus()
})
</script>

<style scoped>
.scheduler-status-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.status-card,
.jobs-card,
.description-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-content {
  min-height: 100px;
}

.status-info {
  padding: 20px 0;
}

.status-item {
  text-align: center;
}

.status-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.status-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.description-content {
  padding: 10px 0;
}
</style>
