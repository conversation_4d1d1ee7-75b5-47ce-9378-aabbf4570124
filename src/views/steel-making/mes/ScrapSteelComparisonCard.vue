<template>
  <el-card class="function-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon>
          <View />
        </el-icon>
        <span>废钢投料信息对比</span>
      </div>
    </template>
    <div class="card-content">
      <p>废钢和转炉报表的数据对不上，分步骤检查</p>
      <el-button type="primary" class="action-btn" @click="showDialog">对比开始日期</el-button>
    </div>
  </el-card>
  <!-- 对话框 -->
  <el-dialog v-model="dialogVisible" title="废钢投料信息对比" width="600px" :clonse-on-click-modal="false" append-to-body>
    <!-- 表单 -->
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <!-- 对比开始日期 -->
      <el-form-item label="对比开始日期" prop="comparisonStartDate">
        <el-date-picker v-model="form.comparisonStartDate" type="date" placeholder="选择对比开始日期" format="YYYY-MM-DD"
          value-format="YYYY-MM-DD" style="width: 100%" />
      </el-form-item>
      <el-form-item label="对比结束日期" prop="comparisonDate">
        <el-date-picker v-model="form.comparisonEndDate" type="date" placeholder="选择对比结束日期" format="YYYY-MM-DD"
          value-format="YYYY-MM-DD" style="width: 100%" />
      </el-form-item>

      <el-form-item label="厂别" prop="factory">
        <el-radio-group v-model="form.factory">
          <el-radio label="L1">一厂</el-radio>
          <el-radio label="L2">二厂</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" :disabled="isRunning">取消</el-button>
        <el-button type="primary" @click="startCheck" :loading="isRunning">
          {{ isRunning ? '对比...' : '开始对比' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage, type FormInstance } from 'element-plus';
import { inject, reactive, ref } from 'vue';
import { useProgressDialog } from '../composables/useProgressDialog';
import { useWebSocket } from '../composables/useWebSocket';
import { scraptSteelComparisonApi, type ScraptSteelComparisonRequest } from '@/api';

const { showProgressDialog, updateProgress, setCompleted, setFailed, closeProgressDialog } = useProgressDialog()
const { connectWebSocket, disconnectWebSocket } = useWebSocket()

const showScrapSteekComparisonResults = inject('showScrapSteekComparisonResults') as (data: any) => void


const dialogVisible = ref(false);
const rules = ref();

// 表单引用和数据
const formRef = ref<FormInstance>()
const form = reactive({
  comparisonStartDate: '',
  comparisonEndDate: '',
  factory: ''
})
const isRunning = ref(false);


const showDialog = () => {
  dialogVisible.value = true;
}

const startCheck = async () => {
  if (!formRef.value) return;

  await formRef.value.validate();
  isRunning.value = true;

  const request: ScraptSteelComparisonRequest = {
    comparisonStartDate: form.comparisonStartDate,
    comparisonEndDate: form.comparisonEndDate,
    factory: form.factory
  }

  const response = await scraptSteelComparisonApi.startComparison(request);

  if (response.code === 200) {
    const taskId = response.data.task_id
    dialogVisible.value = false;

    showProgressDialog('废钢投料对比进度', '任务已启动，正在初始化...')
    connectWebSocket(
      taskId,
      scraptSteelComparisonApi.createWebSocket,
      (message: any) => {
        if (message.type === 'progress') {
          updateProgress(message.data.progress || 0, message.message || '正在对比...')

          if (message.progress === 100 && message.data) {
            isRunning.value = false;
            setCompleted('对比完成')

            ElMessage.success('对比完成')

            const resultData = {
              ...message.data,
              task_id: message.task_id || taskId
            }
            showScrapSteekComparisonResults(resultData)

            setTimeout(() => {
              closeProgressDialog()
              disconnectWebSocket()
            }, 3000)
          }
        } else if (message.type === 'task_completed') {
          isRunning.value = false
          if (message.success) {
            setCompleted(message.result_summary || '废钢投料对比完成')
            ElMessage.success('废钢投料对比完成')
          } else {
            setFailed(message.result_summary || '废钢投料对比失败')
            ElMessage.error('废钢投料对比完成失败')
          }

          setTimeout(() => {
            closeProgressDialog()
            disconnectWebSocket()
          }, 3000)
        }
      }
    )
  } else {
    ElMessage.error('废钢投料对比失败')
  }

}
</script>




<style scoped>
.function-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content p {
  color: #606266;
  margin-bottom: 16px;
}

.action-btn {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
