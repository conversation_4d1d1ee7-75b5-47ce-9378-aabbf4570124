<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="80%"
    :close-on-click-modal="false"
    append-to-body
    @close="handleClose"
  >
    <div v-if="taskDetails" class="task-details">
      <!-- 基本信息 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <span class="detail-card-title">基本信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ taskDetails.id }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ taskDetails.task_name }}</el-descriptions-item>
          <el-descriptions-item label="任务类型">{{ taskDetails.task_type }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(taskDetails.status)">
              {{ getStatusText(taskDetails.status) }}
            </el-tag>
          </el-descriptions-item>
          
          <!-- 数据对比任务特有字段 -->
          <template v-if="taskDetails.task_type === '数据对比'">
            <el-descriptions-item label="开始日期">{{ taskDetails.start_date }}</el-descriptions-item>
            <el-descriptions-item label="结束日期">{{ taskDetails.end_date }}</el-descriptions-item>
            <el-descriptions-item label="厂别">{{ taskDetails.factory }}</el-descriptions-item>
            <el-descriptions-item label="炉号">{{ taskDetails.heat_no || '全部' }}</el-descriptions-item>
            <el-descriptions-item label="物料编码">{{ taskDetails.material_code || '全部' }}</el-descriptions-item>
          </template>
          
          <!-- 炼钢成本数据核对任务特有字段 -->
          <template v-if="taskDetails.task_type === '炼钢成本数据核对'">
            <el-descriptions-item label="核对日期">{{ taskDetails.check_date }}</el-descriptions-item>
            <el-descriptions-item label="厂别">{{ taskDetails.factory }}</el-descriptions-item>
            <el-descriptions-item label="炉号">{{ taskDetails.heat_no || '全部' }}</el-descriptions-item>
            <el-descriptions-item label="物料编码">{{ taskDetails.material_code || '全部' }}</el-descriptions-item>
          </template>
          
          <!-- 电文检查任务特有字段 -->
          <template v-if="taskDetails.task_type === '电文检查' || taskDetails.task_type === 'IX收账作业检查'">
            <el-descriptions-item label="检查日期">{{ taskDetails.check_date }}</el-descriptions-item>
            <el-descriptions-item label="厂别">{{ taskDetails.factory }}</el-descriptions-item>
          </template>
          
          <!-- MES维护任务特有字段 -->
          <template v-if="taskDetails.task_type === 'MES维护'">
            <el-descriptions-item label="维护类型">{{ taskDetails.maintenance_type || '-' }}</el-descriptions-item>
            <el-descriptions-item label="目标日期">{{ taskDetails.target_date || '-' }}</el-descriptions-item>
            <el-descriptions-item label="炉次号">{{ taskDetails.heat_id || '-' }}</el-descriptions-item>
          </template>
          
          <!-- 自动排程任务特有字段 -->
          <template v-if="taskDetails.task_type === '自动排程'">
            <el-descriptions-item label="排程类型">{{ taskDetails.schedule_type || '-' }}</el-descriptions-item>
            <el-descriptions-item label="目标日期">{{ taskDetails.target_date || '-' }}</el-descriptions-item>
          </template>
          
          <el-descriptions-item label="创建时间">{{ formatDateTime(taskDetails.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ taskDetails.started_at ? formatDateTime(taskDetails.started_at) : '-' }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ taskDetails.completed_at ? formatDateTime(taskDetails.completed_at) : '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建人">{{ taskDetails.created_by_name || '-' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 执行统计 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <span class="detail-card-title">执行统计</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ taskDetails.progress || 0 }}%</div>
              <div class="stat-label">执行进度</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ taskDetails.total_records || 0 }}</div>
              <div class="stat-label">总记录数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ taskDetails.processed_records || 0 }}</div>
              <div class="stat-label">已处理记录</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ getStatValue() }}</div>
              <div class="stat-label">{{ getStatLabel() }}</div>
            </div>
          </el-col>
        </el-row>
        <div v-if="taskDetails.duration" class="duration-info">
          <el-tag type="info">执行时长: {{ formatDuration(taskDetails.duration) }}</el-tag>
        </div>
      </el-card>

      <!-- 错误信息 -->
      <el-card v-if="taskDetails.error_message" class="detail-card" shadow="never">
        <template #header>
          <span class="detail-card-title">错误信息</span>
        </template>
        <el-alert
          :title="taskDetails.error_message"
          type="error"
          :closable="false"
          show-icon
        />
      </el-card>

      <!-- 结果摘要 -->
      <el-card v-if="taskDetails.result_summary" class="detail-card" shadow="never">
        <template #header>
          <span class="detail-card-title">结果摘要</span>
        </template>
        <div class="result-summary">
          {{ taskDetails.result_summary }}
        </div>
      </el-card>

      <!-- 结果详情按钮 -->
      <el-card v-if="hasResultData" class="detail-card" shadow="never">
        <template #header>
          <div class="detail-card-header">
            <span class="detail-card-title">{{ getResultTitle() }}</span>
            <el-button type="primary" size="small" @click="showResultDetails">查看详细结果</el-button>
          </div>
        </template>
        <div class="result-preview">
          <slot name="result-preview" :taskDetails="taskDetails">
            <p>点击"查看详细结果"按钮查看完整的{{ getResultTitle() }}数据</p>
          </slot>
        </div>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TaskDetails } from '@/api'

interface Props {
  visible: boolean
  taskDetails: TaskDetails | null
  title?: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'show-result', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '任务详情'
})

const emit = defineEmits<Emits>()

// 计算对话框标题
const dialogTitle = computed(() => {
  if (props.taskDetails?.task_type) {
    return `${props.taskDetails.task_type} - ${props.title}`
  }
  return props.title
})

// 是否有结果数据
const hasResultData = computed(() => {
  return !!(props.taskDetails?.comparison_result || 
           props.taskDetails?.verification_result || 
           props.taskDetails?.check_result ||
           props.taskDetails?.schedule_result ||
           props.taskDetails?.result_data)
})

// 获取结果标题
const getResultTitle = () => {
  const taskType = props.taskDetails?.task_type
  if (taskType === '数据对比') return '对比结果'
  if (taskType === '炼钢成本数据核对') return '核对结果'
  if (taskType === '电文检查' || taskType === 'IX收账作业检查') return '检查结果'
  if (taskType === '自动排程') return '排程结果'
  if (taskType === 'MES维护') return '维护结果'
  return '执行结果'
}

// 获取统计值
const getStatValue = () => {
  const taskType = props.taskDetails?.task_type
  if (taskType === '数据对比' || taskType === '炼钢成本数据核对') {
    return props.taskDetails?.difference_count || 0
  }
  if (taskType === '电文检查' || taskType === 'IX收账作业检查') {
    return props.taskDetails?.error_count || 0
  }
  if (taskType === '自动排程') {
    return props.taskDetails?.success_count || 0
  }
  return 0
}

// 获取统计标签
const getStatLabel = () => {
  const taskType = props.taskDetails?.task_type
  if (taskType === '数据对比' || taskType === '炼钢成本数据核对') {
    return '差异记录数'
  }
  if (taskType === '电文检查' || taskType === 'IX收账作业检查') {
    return '错误记录数'
  }
  if (taskType === '自动排程') {
    return '成功记录数'
  }
  return '特殊记录数'
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '待执行',
    running: '执行中',
    completed: '已完成',
    failed: '失败'
  }
  return textMap[status] || status
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 格式化执行时长
const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟${secs}秒`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 显示结果详情
const showResultDetails = () => {
  const resultData = props.taskDetails?.comparison_result || 
                    props.taskDetails?.verification_result || 
                    props.taskDetails?.check_result ||
                    props.taskDetails?.schedule_result ||
                    props.taskDetails?.result_data
  
  if (resultData) {
    emit('show-result', resultData)
  }
}
</script>

<style scoped>
/* 任务详情样式 */
.task-details {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 20px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.detail-card-title {
  font-weight: 600;
  color: #303133;
}

.detail-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.duration-info {
  margin-top: 20px;
  text-align: center;
}

.result-summary {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  line-height: 1.6;
}

.result-preview {
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  text-align: center;
  color: #606266;
}
</style>
