import apiClient from './config'

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 任务历史查询参数
export interface TaskHistoryQuery {
  task_type?: string
  status?: string
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

// 任务历史项
export interface TaskHistoryItem {
  id: number
  task_name: string
  task_type: string
  status: string
  result_status: string  // 结果状态：有异常/无异常
  created_at: string
  started_at?: string
  completed_at?: string
  error_message?: string
  created_by?: number
  created_by_name?: string
  duration?: number  // 执行时长（秒）

  // 炼钢成本数据核对特有字段 - 改为start_date和end_date
  factory?: string
  heat_no?: string
  material_code?: string

  // 数据对比特有字段
  start_date?: string
  end_date?: string
}

// 任务详情接口
export interface TaskDetails extends TaskHistoryItem {
  // 数据对比任务特有字段
  start_date?: string
  end_date?: string
  factory?: string
  heat_no?: string
  material_code?: string
  total_records?: number
  processed_records?: number
  difference_count?: number
  comparison_result?: any
  verification_result?: any

  // 电文检查任务特有字段
  check_date?: string
  error_count?: number
  check_result?: any

  // MES维护任务特有字段
  maintenance_type?: string
  target_date?: string
  heat_id?: string
  result_data?: any

  // 自动排程任务特有字段
  schedule_type?: string
  success_count?: number
  schedule_result?: any

  // 通用字段
  result_summary?: string
}

// 任务历史响应
export interface TaskHistoryResponse {
  items: TaskHistoryItem[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 调度器状态
export interface SchedulerStatus {
  status: string
  jobs: Array<{
    id: string
    name: string
    next_run_time?: string
    trigger: string
  }>
}

export const maintenanceHistoryApi = {
  /**
   * 获取数据对比任务历史
   */
  async getDataComparisonHistory(params: TaskHistoryQuery): Promise<ApiResponse<TaskHistoryResponse>> {
    const queryParams = new URLSearchParams()
    
    if (params.status) queryParams.append('status', params.status)
    if (params.start_date) queryParams.append('start_date', params.start_date)
    if (params.end_date) queryParams.append('end_date', params.end_date)
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.page_size) queryParams.append('page_size', params.page_size.toString())
    
    return await apiClient.get(`/api/maintenance-history/data-comparison?${queryParams.toString()}`)
  },

  /**
   * 获取ERP电文检查任务历史
   */
  async getErpMessageCheckHistory(params: TaskHistoryQuery): Promise<ApiResponse<TaskHistoryResponse>> {
    const queryParams = new URLSearchParams()
    
    if (params.status) queryParams.append('status', params.status)
    if (params.start_date) queryParams.append('start_date', params.start_date)
    if (params.end_date) queryParams.append('end_date', params.end_date)
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.page_size) queryParams.append('page_size', params.page_size.toString())
    
    return await apiClient.get(`/api/maintenance-history/erp-message-check?${queryParams.toString()}`)
  },

  /**
   * 获取MES电文检查任务历史
   */
  async getMesMessageCheckHistory(params: TaskHistoryQuery): Promise<ApiResponse<TaskHistoryResponse>> {
    const queryParams = new URLSearchParams()
    
    if (params.status) queryParams.append('status', params.status)
    if (params.start_date) queryParams.append('start_date', params.start_date)
    if (params.end_date) queryParams.append('end_date', params.end_date)
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.page_size) queryParams.append('page_size', params.page_size.toString())
    
    return await apiClient.get(`/api/maintenance-history/mes-message-check?${queryParams.toString()}`)
  },

  /**
   * 获取MES维护任务历史
   */
  async getMesMaintenanceHistory(params: TaskHistoryQuery): Promise<ApiResponse<TaskHistoryResponse>> {
    const queryParams = new URLSearchParams()
    
    if (params.status) queryParams.append('status', params.status)
    if (params.start_date) queryParams.append('start_date', params.start_date)
    if (params.end_date) queryParams.append('end_date', params.end_date)
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.page_size) queryParams.append('page_size', params.page_size.toString())
    
    return await apiClient.get(`/api/maintenance-history/mes-maintenance?${queryParams.toString()}`)
  },

  /**
   * 获取自动排程任务历史
   */
  async getAutoScheduleHistory(params: TaskHistoryQuery): Promise<ApiResponse<TaskHistoryResponse>> {
    const queryParams = new URLSearchParams()

    if (params.status) queryParams.append('status', params.status)
    if (params.start_date) queryParams.append('start_date', params.start_date)
    if (params.end_date) queryParams.append('end_date', params.end_date)
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.page_size) queryParams.append('page_size', params.page_size.toString())

    return await apiClient.get(`/api/maintenance-history/auto-schedule?${queryParams.toString()}`)
  },

  /**
   * 获取IX收账作业检查任务历史
   */
  async getIxReceivaHistory(params: TaskHistoryQuery): Promise<ApiResponse<TaskHistoryResponse>> {
    const queryParams = new URLSearchParams()

    if (params.status) queryParams.append('status', params.status)
    if (params.start_date) queryParams.append('start_date', params.start_date)
    if (params.end_date) queryParams.append('end_date', params.end_date)
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.page_size) queryParams.append('page_size', params.page_size.toString())

    return await apiClient.get(`/api/maintenance-history/ix-receiva?${queryParams.toString()}`)
  },

  /**
   * 获取炼钢成本数据核对任务历史
   */
  async getSteelCostDataVerificationHistory(params: TaskHistoryQuery): Promise<ApiResponse<TaskHistoryResponse>> {
    const queryParams = new URLSearchParams()

    if (params.status) queryParams.append('status', params.status)
    if (params.start_date) queryParams.append('start_date', params.start_date)
    if (params.end_date) queryParams.append('end_date', params.end_date)
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.page_size) queryParams.append('page_size', params.page_size.toString())

    return await apiClient.get(`/api/maintenance-history/steel-cost-data-verification?${queryParams.toString()}`)
  },

  /**
   * 获取IX批次检查任务历史
   */
  async getIxBatchCheckHistory(params: TaskHistoryQuery): Promise<ApiResponse<TaskHistoryResponse>> {
    const queryParams = new URLSearchParams()

    if (params.status) queryParams.append('status', params.status)
    if (params.start_date) queryParams.append('start_date', params.start_date)
    if (params.end_date) queryParams.append('end_date', params.end_date)
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.page_size) queryParams.append('page_size', params.page_size.toString())

    return await apiClient.get(`/api/maintenance-history/ix-batch-check?${queryParams.toString()}`)
  },

  /**
   * 获取任务详情
   */
  async getTaskDetails(taskId: number, taskType: string): Promise<ApiResponse<TaskDetails>> {
    return await apiClient.get(`/api/maintenance-history/task/${taskId}/details?task_type=${encodeURIComponent(taskType)}`)
  },

  /**
   * 获取调度器状态
   */
  async getSchedulerStatus(): Promise<ApiResponse<SchedulerStatus>> {
    return await apiClient.get('/api/maintenance-history/scheduler/status')
  }
}
