import { api, BusinessApi, type ApiResult, type PaginationParams, type PaginationResponse } from '../utils'

// 用户相关接口类型定义
export interface User {
  id: number
  username: string
  email: string
  nickname?: string
  remark?: string
  created_at: string
  updated_at: string
}

export interface UserCreate {
  username: string
  email: string
  nickname?: string
  remark?: string
  password: string
}

export interface UserUpdate {
  email?: string
  nickname?: string
  remark?: string
  password?: string
}

/**
 * 重构后的用户API服务
 * 使用新的API工具类，提供更优雅的调用方式
 */
export const userApiRefactored = {
  // 创建用户 - 使用BusinessApi的表单提交方法
  async createUser(userData: UserCreate): Promise<ApiResult<User>> {
    return BusinessApi.submitForm<User>('/api/user/', userData, false, {
      successMessage: '用户创建成功'
    })
  },

  // 获取用户详情 - 使用BusinessApi的获取详情方法
  async getUserDetail(userId: number): Promise<ApiResult<User>> {
    return BusinessApi.getDetails<User>('/api/user', userId)
  },

  // 更新用户 - 使用BusinessApi的表单提交方法
  async updateUser(userId: number, userData: UserUpdate): Promise<ApiResult<User>> {
    return BusinessApi.submitForm<User>(`/api/user/${userId}`, userData, true, {
      successMessage: '用户信息更新成功'
    })
  },

  // 删除用户 - 使用BusinessApi的确认删除方法
  async deleteUser(userId: number): Promise<ApiResult> {
    return BusinessApi.deleteWithConfirm(
      `/api/user/${userId}`,
      '确定要删除该用户吗？此操作不可恢复。'
    )
  },

  // 批量删除用户
  async batchDeleteUsers(userIds: number[]): Promise<ApiResult> {
    return BusinessApi.batchDelete(
      '/api/user/batch-delete',
      userIds,
      `确定要删除选中的 ${userIds.length} 个用户吗？`
    )
  },

  // 获取用户列表 - 使用BusinessApi的分页查询方法
  async getUserList(params: Partial<PaginationParams> = {}): Promise<ApiResult<PaginationResponse<User>>> {
    return BusinessApi.paginateWithDefaults<User>('/api/user/list', params)
  },

  // 搜索用户
  async searchUsers(searchParams: { keyword?: string; status?: string } = {}): Promise<ApiResult<User[]>> {
    return BusinessApi.search<User>('/api/user/search', searchParams)
  },

  // 获取用户角色 - 使用基础api方法
  async getUserRoles(userId: number): Promise<ApiResult> {
    return api.get(`/api/user-role/users/${userId}/roles`)
  },

  // 为用户分配角色 - 使用api.post方法，带成功提示
  async assignRoleToUser(userId: number, roleId: number): Promise<ApiResult> {
    return api.post('/api/user-role/bind', 
      { user_id: userId, role_id: roleId }, 
      {
        showSuccess: true,
        successMessage: '角色分配成功'
      }
    )
  },

  // 移除用户角色 - 使用确认操作
  async removeRoleFromUser(userId: number, roleId: number): Promise<ApiResult> {
    const confirmed = await BusinessApi.confirm('确定要移除该用户的角色吗？', '移除角色确认')
    if (!confirmed) {
      return {
        success: false,
        message: '用户取消操作'
      }
    }

    return api.delete('/api/user-role/unbind', {
      showSuccess: true,
      successMessage: '角色移除成功'
    })
  },

  // 切换用户状态
  async toggleUserStatus(userId: number, status: boolean): Promise<ApiResult> {
    return BusinessApi.toggleStatus('/api/user', userId, status, '用户')
  },

  // 导出用户数据
  async exportUsers(params: any = {}): Promise<ApiResult<Blob>> {
    return BusinessApi.exportData('/api/user/export', params, '用户列表.xlsx')
  },

  // 导入用户数据
  async importUsers(file: File): Promise<ApiResult> {
    return BusinessApi.importData('/api/user/import', file, {
      successMessage: '用户数据导入成功'
    })
  }
}

/**
 * 使用示例：
 * 
 * // 在Vue组件中使用
 * const handleCreateUser = async (userData: UserCreate) => {
 *   const result = await userApiRefactored.createUser(userData)
 *   if (result.success) {
 *     // 创建成功，自动显示成功消息
 *     console.log('创建的用户:', result.data)
 *   }
 *   // 错误会自动显示，无需手动处理
 * }
 * 
 * // 分页查询
 * const loadUsers = async (page: number = 1) => {
 *   const result = await userApiRefactored.getUserList({ page, page_size: 20 })
 *   if (result.success) {
 *     users.value = result.data.items
 *     total.value = result.data.total
 *   }
 * }
 * 
 * // 删除用户（带确认）
 * const handleDeleteUser = async (userId: number) => {
 *   const result = await userApiRefactored.deleteUser(userId)
 *   if (result.success) {
 *     // 删除成功，刷新列表
 *     loadUsers()
 *   }
 * }
 */
