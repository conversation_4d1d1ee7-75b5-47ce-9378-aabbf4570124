<template>
  <div class="user-management">
    <!-- 搜索区域 -->
    <el-form :model="searchForm" inline class="search-form">
      <el-form-item label="关键词">
        <el-input v-model="searchForm.keyword" placeholder="请输入用户名或邮箱" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleCreate">新增用户</el-button>
      <el-button type="danger" @click="handleBatchDelete" :disabled="selectedUsers.length === 0">
        批量删除
      </el-button>
      <el-button type="success" @click="handleExport">导出数据</el-button>
      <el-upload
        action=""
        :before-upload="handleImport"
        :show-file-list="false"
        accept=".xlsx,.xls"
      >
        <el-button type="warning">导入数据</el-button>
      </el-upload>
    </div>

    <!-- 用户列表 -->
    <el-table
      :data="users"
      @selection-change="handleSelectionChange"
      v-loading="loading"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="username" label="用户名" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column prop="nickname" label="昵称" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row.id)">删除</el-button>
          <el-button size="small" :type="row.status ? 'warning' : 'success'" @click="handleToggleStatus(row)">
            {{ row.status ? '禁用' : '启用' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.page_size"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 用户表单对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑用户' : '新增用户'">
      <el-form :model="userForm" :rules="formRules" ref="formRef" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="userForm.nickname" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input v-model="userForm.password" type="password" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="userForm.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import type { FormInstance } from 'element-plus'
import { userApiRefactored, type User, type UserCreate, type UserUpdate } from '../examples/userApiRefactored'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const users = ref<User[]>([])
const selectedUsers = ref<User[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 用户表单
const userForm = reactive<UserCreate & { id?: number }>({
  username: '',
  email: '',
  nickname: '',
  password: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

const formRef = ref<FormInstance>()

// 加载用户列表
const loadUsers = async () => {
  loading.value = true
  const result = await userApiRefactored.getUserList({
    page: pagination.page,
    page_size: pagination.page_size,
    ...searchForm
  })
  
  if (result.success && result.data) {
    users.value = result.data.items
    pagination.total = result.data.total
  }
  loading.value = false
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadUsers()
}

// 重置搜索
const handleReset = () => {
  searchForm.keyword = ''
  pagination.page = 1
  loadUsers()
}

// 新增用户
const handleCreate = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑用户
const handleEdit = (user: User) => {
  isEdit.value = true
  Object.assign(userForm, user)
  dialogVisible.value = true
}

// 删除用户
const handleDelete = async (userId: number) => {
  const result = await userApiRefactored.deleteUser(userId)
  if (result.success) {
    loadUsers()
  }
}

// 批量删除
const handleBatchDelete = async () => {
  const userIds = selectedUsers.value.map(user => user.id)
  const result = await userApiRefactored.batchDeleteUsers(userIds)
  if (result.success) {
    selectedUsers.value = []
    loadUsers()
  }
}

// 切换用户状态
const handleToggleStatus = async (user: User) => {
  const result = await userApiRefactored.toggleUserStatus(user.id, !user.status)
  if (result.success) {
    loadUsers()
  }
}

// 导出数据
const handleExport = async () => {
  await userApiRefactored.exportUsers(searchForm)
}

// 导入数据
const handleImport = async (file: File) => {
  const result = await userApiRefactored.importUsers(file)
  if (result.success) {
    loadUsers()
  }
  return false // 阻止默认上传行为
}

// 表单提交
const handleSubmit = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  submitting.value = true
  
  let result
  if (isEdit.value) {
    const { id, username, password, ...updateData } = userForm
    result = await userApiRefactored.updateUser(id!, updateData as UserUpdate)
  } else {
    result = await userApiRefactored.createUser(userForm as UserCreate)
  }
  
  if (result.success) {
    dialogVisible.value = false
    loadUsers()
  }
  
  submitting.value = false
}

// 选择变化
const handleSelectionChange = (selection: User[]) => {
  selectedUsers.value = selection
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.page_size = size
  pagination.page = 1
  loadUsers()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadUsers()
}

// 重置表单
const resetForm = () => {
  Object.assign(userForm, {
    username: '',
    email: '',
    nickname: '',
    password: '',
    remark: ''
  })
  formRef.value?.clearValidate()
}

// 初始化
onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.toolbar {
  margin-bottom: 20px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
