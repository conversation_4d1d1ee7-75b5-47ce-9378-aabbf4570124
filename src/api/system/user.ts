import apiClient from '../common/config'

// 用户相关接口类型定义
export interface User {
  id: number
  username: string
  nickname?: string
  remark?: string
  status: number
  created_at: string
  updated_at: string
}

export interface UserCreate {
  username: string
  password: string
  nickname?: string
  remark?: string
}

export interface UserUpdate {
  nickname?: string
  remark?: string
  password?: string
}

export interface PaginationParams {
  page: number
  page_size: number
}

export interface PaginationResponse<T> {
  items: T[]
  total: number
  page: number
  page_size: number
}

export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 用户API服务
export const userApi = {
  // 创建用户
  async createUser(userData: UserCreate): Promise<ApiResponse<User>> {
    return await apiClient.post('/api/user/', userData)
  },

  // 获取用户详情
  async getUserDetail(userId: number): Promise<ApiResponse<User>> {
    return await apiClient.post('/api/user/detail', { user_id: userId })
  },

  // 更新用户
  async updateUser(userId: number, userData: UserUpdate): Promise<ApiResponse<User>> {
    return await apiClient.put(`/api/user/${userId}`, userData)
  },

  // 删除用户
  async deleteUser(userId: number): Promise<ApiResponse> {
    return await apiClient.delete(`/api/user/${userId}`)
  },

  // 获取用户列表
  async getUserList(params: PaginationParams): Promise<ApiResponse<PaginationResponse<User>>> {
    return await apiClient.post('/api/user/list', params)
  },

  // 获取用户角色
  async getUserRoles(userId: number): Promise<ApiResponse> {
    return await apiClient.get(`/api/user-role/users/${userId}/roles`)
  },

  // 为用户分配角色
  async assignRoleToUser(userId: number, roleId: number): Promise<ApiResponse> {
    return await apiClient.post('/api/user-role/bind', { user_id: userId, role_id: roleId })
  },

  // 移除用户角色
  async removeRoleFromUser(userId: number, roleId: number): Promise<ApiResponse> {
    return await apiClient.delete('/api/user-role/unbind', { 
      data: { user_id: userId, role_id: roleId } 
    })
  }
}
