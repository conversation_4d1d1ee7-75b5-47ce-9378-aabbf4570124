import apiClient from '../common/config'
import type { ApiResponse } from './user'

// 认证相关接口类型定义
export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
}

export interface UserInfo {
  id: number
  username: string
  nickname?: string
  remark?: string
  status: number
  permissions: string[]
  created_at: string
  updated_at: string
}

// 认证API服务
export const authApi = {
  // 用户登录
  async login(loginData: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    // 使用FormData格式发送登录请求，符合OAuth2PasswordRequestForm格式
    const formData = new FormData()
    formData.append('username', loginData.username)
    formData.append('password', loginData.password)
    
    return await apiClient.post('/api/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })
  },

  // 用户登出
  async logout(): Promise<ApiResponse> {
    return await apiClient.post('/api/auth/logout')
  },

  // 获取当前用户信息
  async getCurrentUser(): Promise<ApiResponse<UserInfo>> {
    return await apiClient.get('/api/auth/me')
  }
}
