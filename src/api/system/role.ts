import apiClient from '../common/config'
import type { PaginationParams, PaginationResponse, ApiResponse } from './user'

// 角色相关接口类型定义
export interface Role {
  id: number
  name: string
  description?: string
  permissions: string[]
  created_at: string
  updated_at: string
}

export interface RoleCreate {
  name: string
  description?: string
  permissions?: string[]
}

export interface RoleUpdate {
  name?: string
  description?: string
  permissions?: string[]
}

// 角色API服务
export const roleApi = {
  // 创建角色
  async createRole(roleData: RoleCreate): Promise<ApiResponse<Role>> {
    return await apiClient.post('/api/role/', roleData)
  },

  // 获取角色详情
  async getRoleDetail(roleId: number): Promise<ApiResponse<Role>> {
    return await apiClient.post('/api/role/detail', { role_id: roleId })
  },

  // 更新角色
  async updateRole(roleId: number, roleData: RoleUpdate): Promise<ApiResponse<Role>> {
    return await apiClient.put(`/api/role/${roleId}`, roleData)
  },

  // 删除角色
  async deleteRole(roleId: number): Promise<ApiResponse> {
    return await apiClient.delete(`/api/role/${roleId}`)
  },

  // 获取角色列表
  async getRoleList(params: PaginationParams): Promise<ApiResponse<PaginationResponse<Role>>> {
    return await apiClient.post('/api/role/list', params)
  },

  // 获取角色用户
  async getRoleUsers(roleId: number): Promise<ApiResponse> {
    return await apiClient.get(`/api/user-role/roles/${roleId}/users`)
  },

  // 获取所有权限列表
  async getAllPermissions(): Promise<ApiResponse> {
    return await apiClient.get('/api/user-role/permissions')
  }
}
