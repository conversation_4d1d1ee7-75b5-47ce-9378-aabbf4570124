# API 层优化文档

## 概述

本项目的API层已经过全面优化，提供了更清晰的结构、更优雅的调用方式和更好的维护性。

## 目录结构

```
src/api/
├── index.ts                    # 统一导出入口
├── common/                     # 通用模块
│   ├── config.ts              # Axios配置
│   ├── maintenanceHistory.ts  # 维护历史API
│   └── index.ts               # 通用模块导出
├── system/                     # 系统管理模块
│   ├── auth.ts                # 认证API
│   ├── user.ts                # 用户管理API
│   ├── role.ts                # 角色管理API
│   ├── log.ts                 # 日志API
│   └── index.ts               # 系统模块导出
├── steel-making/               # 炼钢模块
│   ├── erp/                   # ERP相关API
│   │   ├── dataComparison.ts  # 数据对比
│   │   ├── messageCheck.ts    # 消息检查
│   │   ├── autoSchedule.ts    # 自动调度
│   │   ├── initialCasting.ts  # 初轧
│   │   ├── receiva.ts         # IX收账 (重命名)
│   │   ├── batchCheck.ts      # 批次检查 (重命名)
│   │   ├── steelCostVerification.ts # 钢材成本验证 (重命名)
│   │   ├── heatNoSwitch.ts    # 炉号切换
│   │   ├── scrapSteel.ts      # 废钢对比 (重命名)
│   │   └── index.ts           # ERP模块导出
│   ├── mes/                   # MES相关API
│   │   ├── maintenance.ts     # MES维护 (重命名)
│   │   ├── messageCheck.ts    # MES消息检查 (重命名)
│   │   ├── warehouse.ts       # 仓库管理 (重命名)
│   │   └── index.ts           # MES模块导出
│   └── index.ts               # 炼钢模块导出
├── utils/                      # API工具类
│   ├── ApiClient.ts           # 基础API客户端
│   ├── BusinessApi.ts         # 业务API工具类
│   └── index.ts               # 工具类导出
└── examples/                   # 使用示例
    ├── userApiRefactored.ts   # 重构后的用户API示例
    └── ComponentUsageExample.vue # Vue组件使用示例
```

## 主要改进

### 1. 模块化组织
- 按业务域分组：system、steel-making、common
- 每个模块都有独立的index.ts进行统一导出
- 清晰的层次结构，便于维护和扩展

### 2. 统一导入路径
- 所有API导入都使用 `@/api` 别名
- 消除了复杂的相对路径 `../../../api`
- 提高了代码的可维护性

### 3. 优雅的API调用工具类

#### ApiClient 基础工具类
```typescript
import { api } from '@/api'

// 基础调用
const result = await api.get('/api/users')
const result = await api.post('/api/users', userData)

// 分页查询
const result = await api.paginate('/api/users', { page: 1, page_size: 20 })

// 文件上传
const result = await api.upload('/api/upload', formData)

// 文件下载
const result = await api.download('/api/export', params, 'users.xlsx')
```

#### BusinessApi 业务工具类
```typescript
import { BusinessApi } from '@/api'

// 带确认的删除
const result = await BusinessApi.deleteWithConfirm('/api/users/1')

// 批量删除
const result = await BusinessApi.batchDelete('/api/users/batch', [1, 2, 3])

// 表单提交
const result = await BusinessApi.submitForm('/api/users', userData, false)

// 状态切换
const result = await BusinessApi.toggleStatus('/api/users', 1, true)

// 任务执行
const result = await BusinessApi.executeTask('/api/tasks/run', params, '数据同步')
```

### 4. 统一的响应处理
- 自动处理HTTP状态码
- 统一的错误提示
- 可配置的成功提示
- 标准化的返回格式

### 5. 规范的文件命名
- 移除冗余前缀（如 `mes` 前缀在 mes 目录下）
- 简化长文件名
- 使用清晰的业务术语

## 使用指南

### 基础用法
```typescript
// 在Vue组件中使用
import { userApi, type User } from '@/api'

const loadUsers = async () => {
  const result = await userApi.getUserList({ page: 1, page_size: 20 })
  if (result.success) {
    users.value = result.data.items
  }
}
```

### 使用新的工具类
```typescript
// 使用BusinessApi进行业务操作
import { BusinessApi } from '@/api'

const handleDelete = async (id: number) => {
  const result = await BusinessApi.deleteWithConfirm(`/api/users/${id}`)
  if (result.success) {
    // 删除成功，刷新列表
    loadUsers()
  }
}
```

### 类型安全
```typescript
// 导入类型定义
import type { User, UserCreate, ApiResult } from '@/api'

const createUser = async (userData: UserCreate): Promise<ApiResult<User>> => {
  return BusinessApi.submitForm('/api/users', userData)
}
```

## 迁移指南

### 旧的调用方式
```typescript
// 旧方式 - 需要手动处理状态码和错误
import { userApi } from '../../../api'

const response = await userApi.createUser(userData)
if (response.code === 200) {
  ElMessage.success('创建成功')
  // 处理成功逻辑
} else {
  ElMessage.error(response.msg)
}
```

### 新的调用方式
```typescript
// 新方式 - 自动处理状态码和错误提示
import { BusinessApi } from '@/api'

const result = await BusinessApi.submitForm('/api/users', userData, false, {
  successMessage: '用户创建成功'
})
if (result.success) {
  // 只需处理成功逻辑，错误会自动提示
}
```

## 最佳实践

1. **使用模块导出**：优先从模块根目录导入，而不是直接导入具体文件
2. **使用BusinessApi**：对于常见的业务操作，使用BusinessApi提供的封装方法
3. **类型安全**：始终导入和使用TypeScript类型定义
4. **错误处理**：利用工具类的自动错误处理，减少重复代码
5. **一致性**：在整个项目中保持一致的API调用模式

## 扩展指南

### 添加新的API模块
1. 在相应目录下创建新的API文件
2. 在模块的index.ts中添加导出
3. 遵循现有的命名和结构约定
4. 使用统一的类型定义和响应格式

### 自定义业务工具类
可以基于ApiClient创建特定业务域的工具类：

```typescript
export class SteelMakingApi extends ApiClient {
  async startDataComparison(params: DataComparisonRequest) {
    return this.post('/api/data-comparison/start', params, {
      showSuccess: true,
      successMessage: '数据对比任务已启动'
    })
  }
}
```

这样的优化使得API层更加清晰、易用和可维护。
