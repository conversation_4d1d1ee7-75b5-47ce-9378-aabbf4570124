import { getWebSocketUrl} from '@/config'
import apiClient from '@/api/common/config'

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// MES电文检查请求接口
export interface MESMessageCheckRequest {
  check_date: string    // 检查日期，格式：YYYY-MM-DD
  message_type: string  // 电文类型：receive 或 send
}

// MES电文检查响应接口
export interface MESMessageCheckResponse {
  task_id: number
  message: string
}

// WebSocket消息接口
export interface WebSocketMessage {
  type: string
  task_id: number
  progress?: number
  message?: string
  data?: any
  success?: boolean
  result_summary?: string
}

export const mesMessageCheckApi = {
  /**
   * 启动MES电文检查任务
   */
  async startCheck(request: MESMessageCheckRequest): Promise<ApiResponse<MESMessageCheckResponse>> {
    return await apiClient.post('/api/mes-message-check/start', request)
  },

  /**
   * 获取MES电文检查任务状态
   */
  async getTaskStatus(taskId: number): Promise<ApiResponse<any>> {
    return await apiClient.get(`/api/mes-message-check/task/${taskId}/status`)
  },

  /**
   * 创建MES电文检查WebSocket连接
   */
  createWebSocket(userId: string): WebSocket {
    const wsUrl = `${getWebSocketUrl()}/api/mes-message-check/ws/${userId}`
    return new WebSocket(wsUrl)
  }
}
