import { getWebSocketUrl} from '@/config'
import apiClient from '@/api/common/config'

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 更新钢坯产量调拨汇总报表请求接口
export interface UpdateProdResSummaryRequest {
  date: string  // 日期，格式：YYYY-MM-DD
}

// 更新钢坯产量调拨汇总报表响应接口
export interface UpdateProdResSummaryResponse {
  task_id: number
  message: string
}

// 转炉无法选择废钢号请求接口
export interface BofScrapSteelClearRequest {
  heat_id: string  // 炉次号
}

// 转炉无法选择废钢号响应接口
export interface BofScrapSteelClearResponse {
  task_id: number
  message: string
  original_bo_csmtwo?: string
}

// 任务状态接口
export interface TaskStatus {
  id: number
  task_name: string
  status: string
  progress: number
  error_message?: string
  created_at?: string
  started_at?: string
  completed_at?: string
}

// WebSocket消息接口
export interface WebSocketMessage {
  type: 'task_progress' | 'task_completed'
  task_id: number
  progress?: number
  message?: string
  success?: boolean
  result_summary?: string
  data?: any
  timestamp: number
}

export const mesMaintenanceApi = {
  /**
   * 更新钢坯产量调拨汇总报表
   */
  async updateProdResSummary(request: UpdateProdResSummaryRequest): Promise<ApiResponse<UpdateProdResSummaryResponse>> {
    return await apiClient.post('/api/mes-maintenance/update-prodres-summary', request)
  },

  /**
   * 转炉无法选择废钢号
   */
  async bofScrapSteelClear(request: BofScrapSteelClearRequest): Promise<ApiResponse<BofScrapSteelClearResponse>> {
    return await apiClient.post('/api/mes-maintenance/bof-scrap-steel-clear', request)
  },

  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: number): Promise<ApiResponse<TaskStatus>> {
    return await apiClient.get(`/api/mes-maintenance/task/${taskId}/status`)
  },

  /**
   * 创建WebSocket连接
   */
  createWebSocket(clientId: string): WebSocket {
    const wsUrl = `${getWebSocketUrl()}/api/mes-maintenance/ws/${clientId}`

    console.log('创建MES维护WebSocket连接:', wsUrl)
    return new WebSocket(wsUrl)
  }
}
