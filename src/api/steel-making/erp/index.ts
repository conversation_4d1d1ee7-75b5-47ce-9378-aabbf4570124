// 炼钢ERP相关API统一导出
export { dataComparisonApi } from './dataComparison'
export { messageCheckApi } from './messageCheck'
export { autoScheduleApi } from './autoSchedule'
export { initialCastingApi } from './initialCasting'
export { ixReceivaApi } from './ixReceiva'
export { ixBatchCheckApi } from './ixBatchCheck'
export { steelCostDataVerificationApi } from './steelCostDataVerification'
export { heatNoSwitchApi } from './heatNoSwitch'
export { scraptSteelComparisonApi } from './scrapSteelComparison'

// 导出类型定义
export type {
  DataComparisonRequest,
  TaskStatus,
  ComparisonResult,
  TaskResultsResponse,
  WebSocketMessage,
  ApiResponse
} from './dataComparison'

export type {
  MessageCheckRequest
} from './messageCheck'

export type {
  AutoScheduleRequest
} from './autoSchedule'

export type {
  InitialCastingRequest,
  InitialCastingResponse
} from './initialCasting'

export type {
  IxReceivaCheckRequest,
  IxReceivaCheckResult,
  IxReceivaCheckResponse,
  IxReceivaAutoCheckRequest
} from './ixReceiva'

export type {
  IxBatchCheckRequest,
  IxBatchCheckResponse
} from './ixBatchCheck'

export type {
  SteelCostDataVerificationRequest
} from './steelCostDataVerification'

export type {
  HeatPlanMappingRequest,
  HeatPlanMappingResponse,
  HeatStatusRequest,
  HeatStatusResponse,
  MessageAssistRequest,
  MessageAssistResponse
} from './heatNoSwitch'

export type {
  ScraptSteelComparisonRequest
} from './scrapSteelComparison'
