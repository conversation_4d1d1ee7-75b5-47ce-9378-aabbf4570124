// 炼钢ERP相关API统一导出
export { dataComparisonApi } from './dataComparison'
export { messageCheckApi } from './messageCheck'
export { autoScheduleApi } from './autoSchedule'
export { initialCastingApi } from './initialCasting'
export { ixReceivaApi } from './receiva'
export { ixBatchCheckApi } from './batchCheck'
export { steelCostDataVerificationApi } from './steelCostVerification'
export { heatNoSwitchApi } from './heatNoSwitch'
export { scraptSteelComparisonApi } from './scrapSteel'

// 导出类型定义
export type {
  DataComparisonRequest,
  TaskStatus,
  ComparisonResult,
  TaskResultsResponse,
  WebSocketMessage,
  ApiResponse
} from './dataComparison'

export type {
  MessageCheckRequest
} from './messageCheck'

export type {
  AutoScheduleRequest
} from './autoSchedule'

export type {
  InitialCastingRequest,
  InitialCastingResponse
} from './initialCasting'

export type {
  IxReceivaCheckRequest,
  IxReceivaCheckResult,
  IxReceivaCheckResponse,
  IxReceivaAutoCheckRequest
} from './receiva'

export type {
  IxBatchCheckRequest,
  IxBatchCheckResponse
} from './batchCheck'

export type {
  SteelCostDataVerificationRequest
} from './steelCostVerification'

export type {
  HeatPlanMappingRequest,
  HeatPlanMappingResponse,
  HeatStatusRequest,
  HeatStatusResponse,
  MessageAssistRequest,
  MessageAssistResponse
} from './heatNoSwitch'

export type {
  ScraptSteelComparisonRequest
} from './scrapSteel'
