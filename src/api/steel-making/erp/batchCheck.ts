import { getWebSocketUrl} from '@/config'
import apiClient from '@/api/common/config'

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// IX批次检查响应接口
export interface IxBatchCheckResponse {
  task_id: number
  message: string
}

// 批次错误记录
export interface BatchErrorRecord {
  batchno: string
  sysid: string
  startdate: string
  starttime: string
  execempno: string
  execresult: string
  execmsg: string
}

// 检查结果
export interface IxBatchCheckResult {
  summary: {
    check_time: string
    error_count: number
    system: string
  }
  error_records: BatchErrorRecord[]
}

// WebSocket消息接口
export interface WebSocketMessage {
  type: string
  task_id: number
  progress?: number
  message?: string
  data?: any
  success?: boolean
  result_summary?: string
}

export const ixBatchCheckApi = {
  /**
   * 启动IX批次检查任务
   */
  async startCheck(): Promise<ApiResponse<IxBatchCheckResponse>> {
    return await apiClient.post('/api/ix-batch-check/start')
  },

  /**
   * 获取IX批次检查任务状态
   */
  async getTaskStatus(taskId: number): Promise<ApiResponse<any>> {
    return await apiClient.get(`/api/ix-batch-check/task/${taskId}/status`)
  },

  /**
   * 创建IX批次检查WebSocket连接
   */
  createWebSocket(userId: string): WebSocket {
    const wsUrl = `${getWebSocketUrl()}/api/ix-batch-check/ws/${userId}`

    console.log('创建IX批次检查WebSocket连接:', wsUrl)
    return new WebSocket(wsUrl)
  }
}
