import apiClient from '@/api/common/config'

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 炉号和计划炉号对照关系查询请求
export interface HeatPlanMappingRequest {
  heat_ids: string[]
}

// 炉号和计划炉号对照关系响应
export interface HeatPlanMappingResponse {
  results: Array<{
    heat_id: string
    plan_heat_id: string
    found: boolean
  }>
  total_count: number
}

// 炉号状态查询请求
export interface HeatStatusRequest {
  heat_nos: string[]
}

// 炉号状态查询响应
export interface HeatStatusResponse {
  results: Array<{
    heat_no: string
    status: string
    found: boolean
  }>
  total_count: number
}

// 电文辅助查询请求
export interface MessageAssistRequest {
  heat_no?: string
  check_date?: string  // 默认当天，格式：YYYY-MM-DD
}

// 电文记录
export interface MessageRecord {
  recsysdate: string
  recsystime: string
  heatno: string
  formid: string
  datastr: string
}

// 电文辅助查询响应
export interface MessageAssistResponse {
  records: MessageRecord[]
  total_count: number
  check_date: string
}

export const heatNoSwitchApi = {
  /**
   * 查询炉号和计划炉号对照关系
   */
  async getHeatPlanMapping(request: HeatPlanMappingRequest): Promise<ApiResponse<HeatPlanMappingResponse>> {
    return await apiClient.post('/api/heat-no-switch/heat-plan-mapping', request)
  },

  /**
   * 查询炉号状态
   */
  async getHeatStatus(request: HeatStatusRequest): Promise<ApiResponse<HeatStatusResponse>> {
    return await apiClient.post('/api/heat-no-switch/heat-status', request)
  },

  /**
   * 电文辅助查询
   */
  async getMessageAssist(request: MessageAssistRequest): Promise<ApiResponse<MessageAssistResponse>> {
    return await apiClient.post('/api/heat-no-switch/message-assist', request)
  }
}
