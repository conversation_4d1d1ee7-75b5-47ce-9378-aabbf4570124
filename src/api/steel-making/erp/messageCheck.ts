import { getWebSocketUrl} from '@/config'
import apiClient from '@/api/common/config'

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 电文检查请求接口
export interface MessageCheckRequest {
  check_date: string  // 检查日期，格式：YYYY-MM-DD
  message_type: string  // 电文类型：receive/send
}

// 任务状态接口
export interface TaskStatus {
  id: number
  task_name: string
  status: string
  progress: number
  total_records: number
  processed_records: number
  difference_count: number
  error_message?: string
  comparison_result?: any
  created_at?: string
  started_at?: string
  completed_at?: string
}

// WebSocket消息接口
export interface WebSocketMessage {
  type: 'task_progress' | 'task_completed'
  task_id: number
  progress?: number
  message?: string
  success?: boolean
  result_summary?: string
  data?: any  // 添加额外数据字段
  timestamp: string
}

export const messageCheckApi = {
  /**
   * 启动电文检查任务
   */
  async startMessageCheck(request: MessageCheckRequest): Promise<ApiResponse<{ task_id: number; message: string }>> {
    return await apiClient.post('/api/message-check/start', request)
  },

  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: number): Promise<ApiResponse<TaskStatus>> {
    return await apiClient.get(`/api/message-check/task/${taskId}/status`)
  },

  /**
   * 创建WebSocket连接
   */
  createWebSocket(clientId: string): WebSocket {
    const wsUrl = `${getWebSocketUrl()}/api/message-check/ws/${clientId}`

    console.log('创建电文检查WebSocket连接:', wsUrl)
    return new WebSocket(wsUrl)
  }
}
