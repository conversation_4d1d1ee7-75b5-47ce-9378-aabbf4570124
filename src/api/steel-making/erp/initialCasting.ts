import apiClient from '@/api/common/config'

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 期初连铸资料请求接口
export interface InitialCastingRequest {
  heat_no: string  // 炉号
}

// 期初连铸资料响应接口
export interface InitialCastingResponse {
  sql_statements: string[]  // 生成的SQL语句
  heat_no: string          // 炉号
  generated_count: number  // 生成的SQL数量
}

export const initialCastingApi = {
  /**
   * 生成期初连铸资料SQL
   */
  async generateSQL(request: InitialCastingRequest): Promise<ApiResponse<InitialCastingResponse>> {
    return await apiClient.post('/api/initial-casting/generate-sql', request)
  }
}
