import type { AxiosInstance, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import apiClient from '../common/config'

// 通用API响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 分页参数接口
export interface PaginationParams {
  page: number
  page_size: number
}

// 分页响应接口
export interface PaginationResponse<T> {
  items: T[]
  total: number
  page: number
  page_size: number
}

// API调用选项
export interface ApiOptions {
  showError?: boolean // 是否显示错误消息，默认true
  showSuccess?: boolean // 是否显示成功消息，默认false
  successMessage?: string // 自定义成功消息
  loadingMessage?: string // 加载消息
}

// API调用结果
export interface ApiResult<T = any> {
  success: boolean
  data?: T
  message: string
  code?: number
}

/**
 * 优雅的API调用工具类
 * 统一处理HTTP状态码、错误处理、分页协议等
 */
export class ApiClient {
  private client: AxiosInstance

  constructor(axiosInstance: AxiosInstance = apiClient) {
    this.client = axiosInstance
  }

  /**
   * 处理API响应
   */
  private handleResponse<T>(response: AxiosResponse<ApiResponse<T>>, options: ApiOptions = {}): ApiResult<T> {
    const { showError = true, showSuccess = false, successMessage} = options
    const { code, msg, data } = response.data

    if (code === 200) {
      if (showSuccess) {
        ElMessage.success(successMessage || msg || '操作成功')
      }
      return {
        success: true,
        data,
        message: msg,
        code
      }
    } else {
      if (showError) {
        ElMessage.error(msg || '操作失败')
      }
      return {
        success: false,
        message: msg,
        code
      }
    }
  }

  /**
   * 处理API错误
   */
  private handleError(error: any, options: ApiOptions = {}): ApiResult {
    const { showError = true } = options

    if (showError) {
      const message = error.response?.data?.msg || error.message || '网络错误'
      ElMessage.error(message)
    }

    return {
      success: false,
      message: error.response?.data?.msg || error.message || '网络错误',
      code: error.response?.status
    }
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, params?: any, options: ApiOptions = {}): Promise<ApiResult<T>> {
    try {
      const response = await this.client.get<ApiResponse<T>>(url, { params })
      return this.handleResponse(response, options)
    } catch (error) {
      return this.handleError(error, options)
    }
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, options: ApiOptions = {}): Promise<ApiResult<T>> {
    try {
      const response = await this.client.post<ApiResponse<T>>(url, data)
      return this.handleResponse(response, options)
    } catch (error) {
      return this.handleError(error, options)
    }
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, options: ApiOptions = {}): Promise<ApiResult<T>> {
    try {
      const response = await this.client.put<ApiResponse<T>>(url, data)
      return this.handleResponse(response, options)
    } catch (error) {
      return this.handleError(error, options)
    }
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, options: ApiOptions = {}): Promise<ApiResult<T>> {
    try {
      const response = await this.client.delete<ApiResponse<T>>(url)
      return this.handleResponse(response, options)
    } catch (error) {
      return this.handleError(error, options)
    }
  }

  /**
   * 分页查询
   */
  async paginate<T = any>(
    url: string,
    params: PaginationParams & Record<string, any> = { page: 1, page_size: 10 },
    options: ApiOptions = {}
  ): Promise<ApiResult<PaginationResponse<T>>> {
    return this.get<PaginationResponse<T>>(url, params, options)
  }

  /**
   * 上传文件
   */
  async upload<T = any>(url: string, formData: FormData, options: ApiOptions = {}): Promise<ApiResult<T>> {
    try {
      const response = await this.client.post<ApiResponse<T>>(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return this.handleResponse(response, options)
    } catch (error) {
      return this.handleError(error, options)
    }
  }

  /**
   * 下载文件
   */
  async download(url: string, params?: any, filename?: string): Promise<ApiResult<Blob>> {
    try {
      const response = await this.client.get(url, {
        params,
        responseType: 'blob'
      })

      // 如果提供了文件名，自动下载
      if (filename) {
        const blob = new Blob([response.data])
        const downloadUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(downloadUrl)
      }

      return {
        success: true,
        data: response.data,
        message: '下载成功'
      }
    } catch (error) {
      return this.handleError(error)
    }
  }
}

// 导出默认实例
export const api = new ApiClient()
export default api
