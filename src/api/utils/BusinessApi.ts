import { api, type ApiResult, type ApiOptions, type PaginationParams, type PaginationResponse } from './ApiClient'
import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * 业务API工具类
 * 提供更高级的业务逻辑封装
 */
export class BusinessApi {
  /**
   * 确认操作
   */
  static async confirm(message: string, title: string = '确认操作'): Promise<boolean> {
    try {
      await ElMessageBox.confirm(message, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      return true
    } catch {
      return false
    }
  }

  /**
   * 带确认的删除操作
   */
  static async deleteWithConfirm(
    url: string, 
    message: string = '此操作将永久删除该数据，是否继续？',
    options: ApiOptions = {}
  ): Promise<ApiResult> {
    const confirmed = await this.confirm(message, '删除确认')
    if (!confirmed) {
      return {
        success: false,
        message: '用户取消操作'
      }
    }

    return api.delete(url, {
      showSuccess: true,
      successMessage: '删除成功',
      ...options
    })
  }

  /**
   * 批量删除操作
   */
  static async batchDelete(
    url: string,
    ids: (string | number)[],
    message: string = `确定要删除选中的 ${ids.length} 条数据吗？`,
    options: ApiOptions = {}
  ): Promise<ApiResult> {
    if (ids.length === 0) {
      ElMessage.warning('请选择要删除的数据')
      return {
        success: false,
        message: '未选择数据'
      }
    }

    const confirmed = await this.confirm(message, '批量删除确认')
    if (!confirmed) {
      return {
        success: false,
        message: '用户取消操作'
      }
    }

    return api.post(url, { ids }, {
      showSuccess: true,
      successMessage: `成功删除 ${ids.length} 条数据`,
      ...options
    })
  }

  /**
   * 表单提交（新增/编辑）
   */
  static async submitForm<T = any>(
    url: string,
    data: any,
    isEdit: boolean = false,
    options: ApiOptions = {}
  ): Promise<ApiResult<T>> {
    const method = isEdit ? 'put' : 'post'
    const defaultMessage = isEdit ? '更新成功' : '创建成功'
    
    return api[method]<T>(url, data, {
      showSuccess: true,
      successMessage: defaultMessage,
      ...options
    })
  }

  /**
   * 状态切换操作
   */
  static async toggleStatus(
    url: string,
    id: string | number,
    status: boolean | number,
    statusName: string = '状态',
    options: ApiOptions = {}
  ): Promise<ApiResult> {
    const action = status ? '启用' : '禁用'
    const confirmed = await this.confirm(`确定要${action}该${statusName}吗？`, `${action}确认`)
    
    if (!confirmed) {
      return {
        success: false,
        message: '用户取消操作'
      }
    }

    return api.put(`${url}/${id}/status`, { status }, {
      showSuccess: true,
      successMessage: `${action}成功`,
      ...options
    })
  }

  /**
   * 导出数据
   */
  static async exportData(
    url: string,
    params: any = {},
    filename?: string,
    options: ApiOptions = {}
  ): Promise<ApiResult<Blob>> {
    ElMessage.info('正在导出数据，请稍候...')
    
    const result = await api.download(url, params, filename)
    
    if (result.success) {
      ElMessage.success('导出成功')
    }
    
    return result
  }

  /**
   * 导入数据
   */
  static async importData<T = any>(
    url: string,
    file: File,
    options: ApiOptions = {}
  ): Promise<ApiResult<T>> {
    const formData = new FormData()
    formData.append('file', file)
    
    return api.upload<T>(url, formData, {
      showSuccess: true,
      successMessage: '导入成功',
      ...options
    })
  }

  /**
   * 分页查询（带默认参数）
   */
  static async paginateWithDefaults<T = any>(
    url: string,
    params: Partial<PaginationParams> & Record<string, any> = {},
    options: ApiOptions = {}
  ): Promise<ApiResult<PaginationResponse<T>>> {
    const defaultParams: PaginationParams = {
      page: 1,
      page_size: 10
    }
    
    return api.paginate<T>(url, { ...defaultParams, ...params }, options)
  }

  /**
   * 任务执行（带进度提示）
   */
  static async executeTask<T = any>(
    url: string,
    data: any = {},
    taskName: string = '任务',
    options: ApiOptions = {}
  ): Promise<ApiResult<T>> {
    ElMessage.info(`正在执行${taskName}，请稍候...`)
    
    const result = await api.post<T>(url, data, {
      showSuccess: true,
      successMessage: `${taskName}执行成功`,
      ...options
    })
    
    return result
  }

  /**
   * 数据检查/验证
   */
  static async checkData<T = any>(
    url: string,
    data: any = {},
    checkName: string = '数据检查',
    options: ApiOptions = {}
  ): Promise<ApiResult<T>> {
    return api.post<T>(url, data, {
      showSuccess: false, // 检查操作通常不需要成功提示
      ...options
    })
  }

  /**
   * 获取详情数据
   */
  static async getDetails<T = any>(
    url: string,
    id: string | number,
    options: ApiOptions = {}
  ): Promise<ApiResult<T>> {
    return api.get<T>(`${url}/${id}`, undefined, {
      showError: true,
      ...options
    })
  }

  /**
   * 搜索/筛选数据
   */
  static async search<T = any>(
    url: string,
    searchParams: Record<string, any> = {},
    options: ApiOptions = {}
  ): Promise<ApiResult<T[]>> {
    return api.get<T[]>(url, searchParams, {
      showError: true,
      ...options
    })
  }
}

export default BusinessApi
