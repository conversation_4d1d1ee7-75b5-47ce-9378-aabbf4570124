import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { authApi, type UserInfo, type LoginRequest } from '@/api/system/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const username = computed(() => userInfo.value?.username || '')
  const nickname = computed(() => userInfo.value?.nickname || userInfo.value?.username || '')
  const userId = computed(() => userInfo.value?.id || null)

  // 设置token
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
    // 更新axios默认header
    if (newToken) {
      import('@/api/common/config').then(({ default: apiClient }) => {
        apiClient.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
      })
    }
  }

  // 清除token
  const clearToken = () => {
    token.value = ''
    localStorage.removeItem('token')
    // 清除axios默认header
    import('@/api/common/config').then(({ default: apiClient }) => {
      delete apiClient.defaults.headers.common['Authorization']
    })
  }

  // 设置用户信息
  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
    permissions.value = info.permissions || []
  }

  // 清除用户信息
  const clearUserInfo = () => {
    userInfo.value = null
    permissions.value = []
  }

  // 登录
  const login = async (loginData: LoginRequest) => {
    try {
      const response = await authApi.login(loginData)
      if (response.code === 200) {
        setToken(response.data.access_token)
        // 获取用户信息
        await getCurrentUser()
        return { success: true, message: response.msg }
      } else {
        return { success: false, message: response.msg || '登录失败' }
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.msg || error.message || '登录失败'
      }
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      clearToken()
      clearUserInfo()
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    try {
      const response = await authApi.getCurrentUser()
      if (response.code === 200) {
        setUserInfo(response.data)
        return true
      } else {
        clearToken()
        clearUserInfo()
        return false
      }
    } catch (error) {
      clearToken()
      clearUserInfo()
      return false
    }
  }

  // 检查权限
  const hasPermission = (permission: string) => {
    return permissions.value.includes(permission)
  }

  // 检查多个权限（任一满足）
  const hasAnyPermission = (permissionList: string[]) => {
    return permissionList.some(permission => permissions.value.includes(permission))
  }

  // 检查多个权限（全部满足）
  const hasAllPermissions = (permissionList: string[]) => {
    return permissionList.every(permission => permissions.value.includes(permission))
  }

  // 初始化时设置token到axios
  if (token.value) {
    import('@/api/common/config').then(({ default: apiClient }) => {
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
    })
  }

  return {
    // 状态
    token,
    userInfo,
    permissions,
    // 计算属性
    isLoggedIn,
    username,
    nickname,
    userId,
    // 方法
    setToken,
    clearToken,
    setUserInfo,
    clearUserInfo,
    login,
    logout,
    getCurrentUser,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions
  }
})
