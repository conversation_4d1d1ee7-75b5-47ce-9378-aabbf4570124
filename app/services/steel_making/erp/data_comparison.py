import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from app.models.steel_making.erp.data_comparison import DataComparisonTask
from app.models.common import TaskStatus, Factory
from app.services.common.oracle_connection import oracle_manager
from app.utils.logger import logger

class DataComparisonService:
    """数据对比服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_task(
        self,
        start_date: str,
        end_date: str,
        factory: Factory,
        heat_no: Optional[str] = None,
        material_code: Optional[str] = None,
        user_id: Optional[int] = None
    ) -> DataComparisonTask:
        """创建数据对比任务"""
        task_name = f"每日数据对比_{factory.value}_{start_date}_{end_date}"
        if heat_no:
            task_name += f"_{heat_no}"
        if material_code:
            task_name += f"_{material_code}"

        task = DataComparisonTask(
            task_name=task_name,
            start_date=start_date,
            end_date=end_date,
            factory=factory,
            heat_no=heat_no,
            material_code=material_code,
            created_by=user_id
        )
        
        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)
        
        logger.info(f"创建数据对比任务: {task.id}")
        return task
    
    async def start_task(self, task_id: int, progress_callback: Optional[Callable] = None):
        """启动数据对比任务"""
        try:
            # 更新任务状态为运行中
            await self._update_task_status(task_id, TaskStatus.RUNNING, started_at=datetime.now())
            
            # 获取任务信息
            task = await self._get_task(task_id)
            if not task:
                raise Exception(f"任务 {task_id} 不存在")
            
            # 执行数据对比
            await self._execute_comparison(task, progress_callback)
            
            # 更新任务状态为完成
            await self._update_task_status(
                task_id, 
                TaskStatus.COMPLETED, 
                progress=100,
                completed_at=datetime.now()
            )
            
            logger.info(f"数据对比任务 {task_id} 执行完成")
            
        except Exception as e:
            logger.error(f"数据对比任务 {task_id} 执行失败: {e}")
            await self._update_task_status(
                task_id, 
                TaskStatus.FAILED, 
                error_message=str(e),
                completed_at=datetime.now()
            )
            raise
    
    async def _execute_comparison(self, task: DataComparisonTask, progress_callback: Optional[Callable] = None):
        """执行数据对比逻辑"""
        try:
            # 步骤1：连接数据库
            if progress_callback:
                await progress_callback(task.id, 5, "步骤1：正在连接MES、ERP数据库...")

            # 测试数据库连接
            await self._test_database_connections()

            if progress_callback:
                await progress_callback(task.id, 10, "步骤1：数据库连接成功")

            # 步骤2：获取MES数据（主数据源）
            if progress_callback:
                await progress_callback(task.id, 15, "步骤2：正在查询MES投料消耗数据（主数据源）...")

            mes_data = await self._get_mes_data(task.start_date, task.end_date, task.factory, task.heat_no, task.material_code)

            # MES数据统计
            mes_heat_count = len(set(item['HEATNO'] for item in mes_data))
            mes_material_count = len(set(item['MATERIALCODE'] for item in mes_data))
            mes_total_weight = sum(float(item['TOTAL_WGT'] or 0) for item in mes_data)

            if progress_callback:
                await progress_callback(task.id, 30, f"步骤2：MES投料消耗查询完成 - 炉号{mes_heat_count}个，物料{mes_material_count}个，总投料量{mes_total_weight:.2f}kg")

            # 步骤3：获取ERP数据（对比数据源）
            if progress_callback:
                await progress_callback(task.id, 40, "步骤3：正在查询ERP投料消耗数据（对比数据源）...")

            erp_data = await self._get_erp_data(task.start_date, task.end_date, task.factory, task.heat_no, task.material_code)

            # ERP数据统计
            erp_heat_count = len(set(item['HEATNO'] for item in erp_data))
            erp_material_count = len(set(item['MATERIALCODE'] for item in erp_data))
            erp_total_weight = sum(float(item['TOTAL_WGT'] or 0) for item in erp_data)

            if progress_callback:
                await progress_callback(task.id, 60, f"步骤3：ERP投料消耗查询完成 - 炉号{erp_heat_count}个，物料{erp_material_count}个，总投料量{erp_total_weight:.2f}kg")

            # 步骤4：执行数据对比（以MES为主）
            if progress_callback:
                await progress_callback(task.id, 70, "步骤4：正在执行数据对比分析（以MES数据为主）...")

            differences = await self._compare_data(erp_data, mes_data)

            if progress_callback:
                await progress_callback(task.id, 90, f"步骤4：数据对比完成 - 发现{len(differences)}条差异记录（未发送或不一致）")

            # 步骤4：生成对比结果JSON
            # 按炉号分组差异数据
            differences_by_heat = {}
            for diff in differences:
                heat_no = diff['heat_no']
                if heat_no not in differences_by_heat:
                    differences_by_heat[heat_no] = []
                differences_by_heat[heat_no].append(diff)

            # 构建全局统计数据（JSON字段只存储统计信息）
            comparison_result = {
                "summary": {
                    "comparison_date": f"{task.start_date} 到 {task.end_date}",
                    "factory": task.factory.value,
                    "comparison_mode": "MES为主",
                    "total_records": len(mes_data) + len(erp_data),
                    "difference_count": len(differences)
                },
                "statistics": {
                    "mes_stats": {
                        "record_count": len(mes_data),
                        "heat_count": mes_heat_count,
                        "material_count": mes_material_count,
                        "total_weight": mes_total_weight,
                        "avg_weight_per_heat": mes_total_weight / mes_heat_count if mes_heat_count > 0 else 0
                    },
                    "erp_stats": {
                        "record_count": len(erp_data),
                        "heat_count": erp_heat_count,
                        "material_count": erp_material_count,
                        "total_weight": erp_total_weight,
                        "avg_weight_per_heat": erp_total_weight / erp_heat_count if erp_heat_count > 0 else 0
                    },
                    "coverage": {
                        "heat_coverage_rate": (erp_heat_count / mes_heat_count * 100) if mes_heat_count > 0 else 0,
                        "material_coverage_rate": (erp_material_count / mes_material_count * 100) if mes_material_count > 0 else 0,
                        "weight_coverage_rate": (erp_total_weight / mes_total_weight * 100) if mes_total_weight > 0 else 0
                    }
                },
                "difference_summary": {
                    "by_status": self._count_differences_by_status(differences),
                    "by_heat": self._count_differences_by_heat(differences),
                    "weight_analysis": {
                        "total_positive_diff": sum(diff.get('difference', 0) for diff in differences if diff.get('difference', 0) > 0),
                        "total_negative_diff": sum(diff.get('difference', 0) for diff in differences if diff.get('difference', 0) < 0),
                        "avg_difference": sum(diff.get('difference', 0) for diff in differences) / len(differences) if differences else 0,
                        "max_difference": max((diff.get('difference', 0) for diff in differences), default=0),
                        "min_difference": min((diff.get('difference', 0) for diff in differences), default=0)
                    }
                }
            }

            # 更新任务统计信息和结果，保存明细数据到专门的表
            await self._update_task_with_result(task.id, len(erp_data) + len(mes_data), len(differences), comparison_result)
            await self._save_comparison_details(task.id, differences)

            if progress_callback:
                await progress_callback(task.id, 100, "数据对比完成", comparison_result)

        except Exception as e:
            logger.error(f"执行数据对比失败: {e}")
            raise

    async def _test_database_connections(self):
        """测试数据库连接"""
        def test_connections():
            # 测试ERP连接
            erp_conn = oracle_manager.get_erp_connection()
            try:
                oracle_manager.execute_query(erp_conn, "SELECT 1 FROM DUAL", {})
                logger.info("ERP数据库连接测试成功")
            finally:
                oracle_manager.close_connection(erp_conn)

            # 测试MES连接
            mes_conn = oracle_manager.get_mes_connection()
            try:
                oracle_manager.execute_query(mes_conn, "SELECT 1 FROM DUAL", {})
                logger.info("MES数据库连接测试成功")
            finally:
                oracle_manager.close_connection(mes_conn)

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, test_connections)

    async def _get_erp_data(self, start_date: str, end_date: str, factory: Factory, heat_no: Optional[str] = None, material_code: Optional[str] = None) -> List[Dict]:
        """获取ERP数据"""
        def query_erp():
            logger.info(f"开始连接ERP数据库，厂别: {factory.value}, 日期范围: {start_date} 到 {end_date}")
            connection = oracle_manager.get_erp_connection()
            try:
                # 根据厂别确定工作车间
                # B为一厂，D为二厂
                workshop = 'B' if factory == Factory.FACTORY_1 else 'D'
                logger.info(f"ERP工作车间: {workshop}")

                # 构建日期范围和厂别的SQL
                sql = """
                SELECT HEATNO, MATERIALCODE, SUM(MATERIALDIFFWGT) as TOTAL_WGT
                FROM DB.TBOJ32
                WHERE HEATNO IN (
                    SELECT HEATNO FROM DB.TBIS101
                    WHERE ACCTDATE BETWEEN :start_date AND :end_date
                    UNION
                    SELECT HEATNO FROM DB.TBIB101
                    WHERE ACCTDATE BETWEEN :start_date AND :end_date
                )
                AND WORKSHOP = :workshop
                AND MATERIALTYPE != 9
                """

                params = {
                    'workshop': workshop,
                    'start_date': start_date.replace('-', ''),
                    'end_date': end_date.replace('-', '')
                }

                # 添加可选的炉号筛选
                if heat_no:
                    sql += " AND HEATNO = :heat_no"
                    params['heat_no'] = heat_no

                # 添加可选的物料编码筛选
                if material_code:
                    sql += " AND MATERIALCODE = :material_code"
                    params['material_code'] = material_code

                sql += " GROUP BY HEATNO, MATERIALCODE"

                logger.info(f"执行ERP查询，参数: {params}")
                results = oracle_manager.execute_query(connection, sql, params)
                logger.info(f"ERP数据查询成功，获取到 {len(results)} 条记录")
                return results
            finally:
                oracle_manager.close_connection(connection)

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, query_erp)
    
    async def _get_mes_data(self, start_date: str, end_date: str, factory: Factory, heat_no: Optional[str] = None, material_code: Optional[str] = None) -> List[Dict]:
        """获取MES数据"""
        def query_mes():
            logger.info(f"开始连接MES数据库，厂别: {factory.value}, 日期范围: {start_date} 到 {end_date}")
            connection = oracle_manager.get_mes_connection()
            try:
                # 根据厂别确定工作车间
                # L1为一厂，L2为二厂
                work_shop = 'L1' if factory == Factory.FACTORY_1 else 'L2'
                logger.info(f"工作车间: {work_shop}")

                sql = """
                SELECT heat_id as HEATNO, MAT_CODE as MATERIALCODE, sum(MAT_WGT) as TOTAL_WGT
                FROM JGLGMES.SMES_B_MAT_USE t
                WHERE WORK_SHOP = :work_shop
                  AND HEAT_ID in (
                      SELECT DISTINCT heat_id
                      FROM JGLGMES.SMES_B_CCMRES
                      WHERE WORK_SHOP = :work_shop
                        AND STAR_CAST_TIM BETWEEN TO_DATE(:start_date || ' 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                          AND TO_DATE(:end_date || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
                  )
                """

                params = {
                    'work_shop': work_shop,
                    'start_date': start_date,
                    'end_date': end_date
                }

                # 添加可选的炉号筛选
                if heat_no:
                    sql += " AND HEAT_ID = :heat_no"
                    params['heat_no'] = heat_no

                # 添加可选的物料编码筛选
                if material_code:
                    sql += " AND MAT_CODE = :material_code"
                    params['material_code'] = material_code

                sql += """
                GROUP BY HEAT_ID, MAT_CODE
                HAVING sum(MAT_WGT) IS NOT NULL AND sum(MAT_WGT) != 0
                """

                logger.info(f"执行MES查询，参数: {params}")
                results = oracle_manager.execute_query(connection, sql, params)
                logger.info(f"MES数据查询成功，获取到 {len(results)} 条记录")
                return results
            finally:
                oracle_manager.close_connection(connection)

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, query_mes)
    
    async def _compare_data(self, erp_data: List[Dict], mes_data: List[Dict]) -> List[Dict]:
        """对比ERP和MES数据 - 以MES数据为主进行对比"""
        differences = []

        # 将ERP数据转换为字典以便快速查找
        erp_dict = {}
        for item in erp_data:
            key = f"{item['HEATNO']}_{item['MATERIALCODE']}"
            erp_dict[key] = float(item['TOTAL_WGT'] or 0)

        # 以MES数据为主进行对比
        for mes_item in mes_data:
            heat_no = mes_item['HEATNO']
            material_code = mes_item['MATERIALCODE']
            mes_value = round(float(mes_item['TOTAL_WGT'] or 0))
            if material_code == "1001002" or material_code == "1001001":
                continue

            key = f"{heat_no}_{material_code}"
            erp_value = erp_dict.get(key, 0)

            # 记录所有MES数据的对比情况：
            # 1. MES有数据但ERP没有数据（可能未发送给ERP）
            # 2. 两边都有数据但数值不同
            # 3. MES有数据且ERP也有数据且一致（可选择是否记录）
            if mes_value > 0.001:  # 只处理有意义的MES数据
                difference = mes_value - erp_value  # 注意：这里改为MES - ERP
                difference_rate = 0

                # 计算差异率
                if mes_value != 0:
                    difference_rate = (difference / mes_value * 100)
                elif erp_value != 0:
                    difference_rate = -100.0  # ERP有数据但MES没有（理论上不会发生，因为我们以MES为主）

                # 记录差异情况
                if abs(difference) > 0.001:  # 有差异的情况
                    status = "未发送" if erp_value == 0 else "数据不一致"

                    differences.append({
                        'heat_no': heat_no,
                        'material_code': material_code,
                        'mes_value': mes_value,
                        'erp_value': erp_value,
                        'difference': difference,
                        'difference_rate': difference_rate,
                        'status': status,
                        'description': f"MES数据: {mes_value}kg, ERP数据: {erp_value}kg"
                    })

        # 可选：检查ERP中有但MES中没有的数据（这种情况理论上不应该存在）
        mes_keys = {f"{item['HEATNO']}_{item['MATERIALCODE']}" for item in mes_data}
        for item in erp_data:
            key = f"{item['HEATNO']}_{item['MATERIALCODE']}"
            if key not in mes_keys:
                erp_value = float(item['TOTAL_WGT'] or 0)
                if erp_value > 0.001:  # 只记录有意义的ERP数据
                    differences.append({
                        'heat_no': item['HEATNO'],
                        'material_code': item['MATERIALCODE'],
                        'mes_value': 0,
                        'erp_value': erp_value,
                        'difference': -erp_value,  # MES - ERP = 0 - ERP
                        'difference_rate': -100.0,
                        'status': "仅ERP有数据",
                        'description': f"MES数据: 0kg, ERP数据: {erp_value}kg (异常情况)"
                    })

        logger.info(f"数据对比完成（以MES为主），发现 {len(differences)} 条差异记录")
        return differences
    
    async def _update_task_with_result(self, task_id: int, total_records: int, difference_count: int, comparison_result: Dict):
        """更新任务统计信息和结果JSON"""
        stmt = update(DataComparisonTask).where(
            DataComparisonTask.id == task_id
        ).values(
            total_records=total_records,
            processed_records=total_records,
            difference_count=difference_count,
            comparison_result=comparison_result  # 存储JSON结果
        )

        await self.db.execute(stmt)
        await self.db.commit()
        logger.info(f"更新任务统计信息，总记录数: {total_records}, 差异数: {difference_count}")

    def _count_differences_by_status(self, differences: List[Dict]) -> Dict[str, int]:
        """按状态统计差异数量"""
        status_count = {}
        for diff in differences:
            status = diff.get('status', '未知')
            status_count[status] = status_count.get(status, 0) + 1
        return status_count

    def _count_differences_by_heat(self, differences: List[Dict]) -> Dict[str, int]:
        """按炉号统计差异数量"""
        heat_count = {}
        for diff in differences:
            heat_no = diff.get('heat_no', '未知')
            heat_count[heat_no] = heat_count.get(heat_no, 0) + 1
        return heat_count

    async def _save_comparison_details(self, task_id: int, differences: List[Dict]):
        """保存对比差异明细到专门的表中"""
        try:
            from app.models.steel_making.common.task_result_details import DataComparisonDetail
            from sqlalchemy import delete

            # 删除旧的明细记录
            delete_stmt = delete(DataComparisonDetail).where(
                DataComparisonDetail.task_id == task_id
            )
            await self.db.execute(delete_stmt)

            # 批量插入新的明细记录
            if differences:
                detail_records = []
                for diff in differences:
                    detail = DataComparisonDetail(
                        task_id=task_id,
                        heat_no=diff.get('heat_no', ''),
                        material_code=diff.get('material_code', ''),
                        mes_value=diff.get('mes_value'),
                        erp_value=diff.get('erp_value'),
                        difference=diff.get('difference'),
                        difference_rate=diff.get('difference_rate'),
                        status=diff.get('status', ''),
                        description=diff.get('description', '')
                    )
                    detail_records.append(detail)

                self.db.add_all(detail_records)
                await self.db.commit()
                logger.info(f"保存了 {len(detail_records)} 条对比差异明细记录")

        except Exception as e:
            logger.error(f"保存对比差异明细失败: {e}")
            await self.db.rollback()
            raise
    
    async def _update_task_status(
        self, 
        task_id: int, 
        status: TaskStatus, 
        progress: Optional[int] = None,
        error_message: Optional[str] = None,
        started_at: Optional[datetime] = None,
        completed_at: Optional[datetime] = None
    ):
        """更新任务状态"""
        update_data = {'status': status}
        
        if progress is not None:
            update_data['progress'] = progress
        if error_message is not None:
            update_data['error_message'] = error_message
        if started_at is not None:
            update_data['started_at'] = started_at
        if completed_at is not None:
            update_data['completed_at'] = completed_at
        
        stmt = update(DataComparisonTask).where(
            DataComparisonTask.id == task_id
        ).values(**update_data)
        
        await self.db.execute(stmt)
        await self.db.commit()
    
    async def _get_task(self, task_id: int) -> Optional[DataComparisonTask]:
        """获取任务信息"""
        stmt = select(DataComparisonTask).where(DataComparisonTask.id == task_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_task_status(self, task_id: int) -> Optional[Dict]:
        """获取任务状态"""
        task = await self._get_task(task_id)
        if not task:
            return None

        return {
            'id': task.id,
            'task_name': task.task_name,
            'status': task.status.value,
            'progress': task.progress,
            'total_records': task.total_records,
            'processed_records': task.processed_records,
            'difference_count': task.difference_count,
            'error_message': task.error_message,
            'comparison_result': task.comparison_result,  # JSON结果
            'created_at': task.created_at.isoformat() if task.created_at else None,
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None
        }
