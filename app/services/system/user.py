from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError
from app.models.system.user import User
from app.utils.logger import logger
from app.exceptions import UserAlreadyExists, UserNotFound, DatabaseException

class UserService:
    """用户服务"""

    @staticmethod
    async def create_user(db: AsyncSession, username: str, password: str,
                         nickname: Optional[str] = None, remark: Optional[str] = None) -> User:
        """创建用户"""
        try:
            # 检查用户名是否已存在
            existing_user = await UserService.get_user_by_username(db, username)
            if existing_user:
                raise UserAlreadyExists("用户名已存在")

            # 创建新用户
            user = User(username=username, nickname=nickname, remark=remark)
            user.set_password(password)
            db.add(user)
            await db.commit()
            await db.refresh(user)
            return user
        except UserAlreadyExists:
            await db.rollback()
            raise
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"创建用户数据库操作失败: {str(e)}")
            raise DatabaseException("创建用户失败")
        except Exception as e:
            await db.rollback()
            logger.error(f"创建用户失败: {str(e)}")
            raise

    @staticmethod
    async def get_user_by_id(db: AsyncSession, user_id: int) -> User:
        """根据ID获取用户"""
        try:
            result = await db.execute(select(User).where(User.id == user_id))
            user = result.scalars().first()
            if not user:
                raise UserNotFound("用户不存在")
            return user
        except UserNotFound:
            raise
        except SQLAlchemyError as e:
            logger.error(f"获取用户数据库操作失败: {str(e)}")
            raise DatabaseException("获取用户失败")
        except Exception as e:
            logger.error(f"获取用户失败: {str(e)}")
            raise

    @staticmethod
    async def get_user_by_username(db: AsyncSession, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        try:
            result = await db.execute(select(User).where(User.username == username))
            return result.scalars().first()
        except Exception as e:
            logger.error(f"查询用户失败: {str(e)}")
            raise

    @staticmethod
    async def update_user(db: AsyncSession, user_id: int, **kwargs) -> User:
        """更新用户信息"""
        try:
            result = await db.execute(select(User).where(User.id == user_id))
            user = result.scalars().first()
            if not user:
                raise UserNotFound("用户不存在")

            for key, value in kwargs.items():
                if hasattr(user, key) and key != "id":
                    if key == "password":
                        user.set_password(value)
                    else:
                        setattr(user, key, value)

            await db.commit()
            await db.refresh(user)
            return user
        except Exception as e:
            await db.rollback()
            logger.error(f"更新用户失败: {str(e)}")
            raise

    @staticmethod
    async def delete_user(db: AsyncSession, user_id: int) -> None:
        """删除用户"""
        try:
            result = await db.execute(select(User).where(User.id == user_id))
            user = result.scalars().first()
            if not user:
                raise UserNotFound("用户不存在")

            await db.delete(user)
            await db.commit()
        except Exception as e:
            await db.rollback()
            logger.error(f"删除用户失败: {str(e)}")
            raise

    @staticmethod
    async def list_users(db: AsyncSession, skip: int = 0, limit: int = 10) -> list[User]:
        """获取用户列表"""
        try:
            result = await db.execute(select(User).offset(skip).limit(limit))
            return result.scalars().all()
        except Exception as e:
            logger.error(f"获取用户列表失败: {str(e)}")
            raise

    @staticmethod
    async def count_users(db: AsyncSession) -> int:
        """获取用户总数"""
        try:
            result = await db.execute(select(User))
            return len(result.scalars().all())
        except Exception as e:
            logger.error(f"获取用户总数失败: {str(e)}")
            raise

    @staticmethod
    async def get_user_roles(db: AsyncSession, user_id: int) -> list:
        """获取用户的所有角色"""
        try:
            from app.models.system.user_role import UserRole
            from app.models.system.role import Role

            result = await db.execute(
                select(Role).join(UserRole, Role.id == UserRole.role_id)
                .where(UserRole.user_id == user_id)
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"获取用户角色失败: {str(e)}")
            raise

    @staticmethod
    async def assign_role_to_user(db: AsyncSession, user_id: int, role_id: int) -> bool:
        """为用户分配角色"""
        try:
            from app.models.system.user_role import UserRole

            # 检查关联是否已存在
            existing = await db.execute(
                select(UserRole).where(
                    UserRole.user_id == user_id,
                    UserRole.role_id == role_id
                )
            )
            if existing.scalars().first():
                return False  # 关联已存在

            # 创建新关联
            user_role = UserRole(user_id=user_id, role_id=role_id)
            db.add(user_role)
            await db.commit()
            return True
        except Exception as e:
            await db.rollback()
            logger.error(f"分配角色失败: {str(e)}")
            raise

    @staticmethod
    async def remove_role_from_user(db: AsyncSession, user_id: int, role_id: int) -> bool:
        """移除用户角色"""
        try:
            from app.models.system.user_role import UserRole

            result = await db.execute(
                select(UserRole).where(
                    UserRole.user_id == user_id,
                    UserRole.role_id == role_id
                )
            )
            user_role = result.scalars().first()
            if user_role:
                await db.delete(user_role)
                await db.commit()
                return True
            return False
        except Exception as e:
            await db.rollback()
            logger.error(f"移除用户角色失败: {str(e)}")
            raise