from datetime import datetime
from contextlib import contextmanager
from typing import Optional
from fastapi import Request, Depends
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.models.system.operation_log import OperationLog
from app.database.session import get_db
from app.services.system.auth import get_current_user
from app.utils import logger


class OperationLogService:
    """操作日志服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.log = None
        self.start_time = None

    @staticmethod
    async def list_logs(db: AsyncSession, skip: int = 0, limit: int = 10) -> list[OperationLog]:
        """获取日志列表"""
        try:
            result = await db.execute(select(OperationLog).offset(skip).limit(limit))
            return result.scalars().all()
        except Exception as e:
            logger.error(f"获取日志列表失败: {str(e)}")
            raise
    

    async def count_logs(db: AsyncSession) -> int:
        """获取用户总数"""
        try:
            result = await db.execute(select(OperationLog))
            return len(result.scalars().all())
        except Exception as e:
            logger.error(f"获取日志总数失败: {str(e)}")
            raise