from fastapi import status
from typing import TypeVar, Generic, Optional, Any, Dict
from pydantic import BaseModel

T = TypeVar('T')

class ResponseModel(BaseModel):
    code: int
    msg: str
    data: Optional[T] = None

def success(data: Any = None, msg: str = "操作成功") -> Dict[str, Any]:
    """成功响应"""
    return {
        "code": 200,
        "msg": msg,
        "data": data
    }

def fail(msg: str = "操作失败", data: Any = None) -> Dict[str, Any]:
    """失败响应"""
    return {
        "code": -1,
        "msg": msg,
        "data": data
    }

def error(msg: str = "服务器异常", data: Any = None) -> Dict[str, Any]:
    """错误响应"""
    return {
        "code": 500,
        "msg": msg,
        "data": data
    }

def unauthorized(msg: str = "未授权", data: Any = None) -> Dict[str, Any]:
    """错误响应"""
    return {
        "code": 401,
        "msg": msg,
        "data": data
    }