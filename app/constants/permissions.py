"""系统权限定义"""

PERMISSIONS = {
    "USER": {
        "VIEW": "user:view",
        "CREATE": "user:create",
        "UPDATE": "user:update",
        "DELETE": "user:delete"
    },
    "ROLE": {
        "VIEW": "role:view",
        "CREATE": "role:create",
        "UPDATE": "role:update",
        "DELETE": "role:delete"
    },
    "LOG": {
        "VIEW": "log:view"
    },
    # 炼钢ERP维护权限
    "STEEL_ERP": {
        "DATA_COMPARISON": "steel_erp:data_comparison",
        "AUTO_SCHEDULE": "steel_erp:auto_schedule",
        "HEAT_NO_SWITCH": "steel_erp:heat_no_switch",
        "INITIAL_CASTING": "steel_erp:initial_casting",
        "IX_RECEIVA": "steel_erp:ix_receiva",
        "IX_BATCH_CHECK": "steel_erp:ix_batch_check",
        "STEEL_COST_VERIFICATION": "steel_erp:steel_cost_verification",
        "MESSAGE_CHECK": "steel_erp:message_check",
        "MAINTENANCE_HISTORY": "steel_erp:maintenance_history"
    },
    # 炼钢MES维护权限
    "STEEL_MES": {
        "MES_MAINTENANCE": "steel_mes:mes_maintenance",
        "MES_MESSAGE_CHECK": "steel_mes:mes_message_check",
        "MANUAL_WAREHOUSE": "steel_mes:manual_warehouse"
    },
    # 炼钢通用权限
    "STEEL_COMMON": {
        "TASK_RESULT_DETAILS": "steel_common:task_result_details"
    }
}

def get_all_permissions():
    """获取所有权限列表"""
    permissions = []
    for category in PERMISSIONS.values():
        permissions.extend(category.values())
    return permissions