"""
权限装饰器和操作记录装饰器
"""

from functools import wraps
from typing import List, Union, Optional
from datetime import datetime
from fastapi import HTTPException, status, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.session import get_db
from app.services.system.auth import get_current_user
from app.models.system.user import User
from app.models.system.operation_log import OperationLog
from app.utils.logger import logger


def require_permissions(permissions: Union[str, List[str]]):
    """
    权限检查装饰器
    
    Args:
        permissions: 需要的权限，可以是单个权限字符串或权限列表
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user
            current_user = None
            for key, value in kwargs.items():
                if isinstance(value, User):
                    current_user = value
                    break
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未找到用户信息"
                )
            
            # 检查权限
            required_perms = permissions if isinstance(permissions, list) else [permissions]
            user_permissions = getattr(current_user, 'permissions', []) or []
            
            # 检查用户是否拥有所需权限
            for perm in required_perms:
                if perm not in user_permissions:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"权限不足，需要权限: {perm}"
                    )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_any_permission(permissions: List[str]):
    """
    权限检查装饰器 - 只需要拥有其中任意一个权限即可
    
    Args:
        permissions: 权限列表，用户只需拥有其中任意一个即可
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user
            current_user = None
            for key, value in kwargs.items():
                if isinstance(value, User):
                    current_user = value
                    break
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未找到用户信息"
                )
            
            # 检查权限
            user_permissions = getattr(current_user, 'permissions', []) or []
            
            # 检查用户是否拥有任意一个所需权限
            has_permission = any(perm in user_permissions for perm in permissions)
            
            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要以下权限之一: {', '.join(permissions)}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def log_operation(operation_type: str, operation_content: str):
    """
    操作记录装饰器

    Args:
        operation_type: 操作类型，如 "用户管理", "角色管理", "数据对比" 等
        operation_content: 操作内容描述，如 "创建用户", "删除角色", "启动数据对比" 等
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取必要的参数
            current_user = None
            db = None
            request = None

            for key, value in kwargs.items():
                if isinstance(value, User):
                    current_user = value
                elif isinstance(value, AsyncSession):
                    db = value
                elif isinstance(value, Request):
                    request = value

            # 如果没有找到必要参数，直接执行原函数
            if not current_user or not db:
                return await func(*args, **kwargs)

            # 记录操作开始时间
            start_time = datetime.now()
            operation_log = None

            try:
                # 创建操作日志记录
                operation_log = OperationLog(
                    operation_type=operation_type,
                    operation_content=operation_content,
                    operator_id=current_user.id,
                    operator_name=current_user.username,
                    ip_address=request.client.host if request and request.client else None,
                    user_agent=request.headers.get("user-agent") if request else None,
                    status="processing"
                )

                # 执行原函数
                result = await func(*args, **kwargs)

                # 标记操作成功
                operation_log.status = "success"

                return result

            except Exception as e:
                # 标记操作失败
                if operation_log:
                    operation_log.status = "failed"
                    operation_log.error_message = str(e)

                logger.error(f"操作失败: {operation_type} - {operation_content}, 错误: {str(e)}")
                raise

            finally:
                # 记录操作耗时并保存日志
                if operation_log:
                    operation_log.duration = (datetime.now() - start_time).total_seconds()
                    db.add(operation_log)
                    try:
                        await db.commit()
                    except Exception as e:
                        logger.error(f"保存操作日志失败: {str(e)}")
                        await db.rollback()

        return wrapper
    return decorator
