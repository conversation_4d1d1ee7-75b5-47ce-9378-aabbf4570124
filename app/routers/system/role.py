from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from pydantic import BaseModel

from app.database import get_db
from app.domain.response import success, fail
from app.utils.logger import logger
from app.utils.decorators import require_permissions, log_operation
from app.constants.permissions import PERMISSIONS
from app.services.system.role import RoleService
from app.services.system.auth import get_current_user
from app.models.system.user import User

router = APIRouter(prefix="/api/role", tags=["role"])

# 请求模型
class RoleCreate(BaseModel):
    name: str
    description: Optional[str] = None
    permissions: Optional[list[str]] = None

class RoleUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    permissions: Optional[list[str]] = None

class PaginationParams(BaseModel):
    page: int = 1
    page_size: int = 10

# 响应模型
class RoleResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    permissions: list[str]
    created_at: str
    updated_at: str

@router.post("/")
@require_permissions(PERMISSIONS["ROLE"]["CREATE"])
async def create_role(
    role: RoleCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建角色"""
    created_role = await RoleService.create_role(
        db,
        name=role.name,
        description=role.description,
        permissions=role.permissions
    )
    return success(data=created_role)

@router.post("/detail")
async def get_role(role_id: int, db: AsyncSession = Depends(get_db)):
    """获取角色信息"""
    role = await RoleService.get_role_by_id(db, role_id)
    return success(data=role)

@router.put("/{role_id}")
@log_operation("角色管理", "更新角色")
async def update_role(
    role_id: int,
    role: RoleUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新角色信息"""
    update_data = role.model_dump(exclude_unset=True)
    updated_role = await RoleService.update_role(db, role_id, **update_data)
    return success(data=updated_role)

@router.delete("/{role_id}")
@log_operation("角色管理", "角色删除")
async def delete_role(role_id: int, db: AsyncSession = Depends(get_db)):
    """删除角色"""
    await RoleService.delete_role(db, role_id)
    return success(msg="角色删除成功")

@router.post("/list")
async def list_roles(
    params: PaginationParams,
    db: AsyncSession = Depends(get_db)
):
    """获取角色列表"""
    skip = (params.page - 1) * params.page_size
    roles = await RoleService.list_roles(db, skip=skip, limit=params.page_size)
    return success(data={
        "items": roles,
        "total": await RoleService.count_roles(db),
        "page": params.page,
        "page_size": params.page_size
    })