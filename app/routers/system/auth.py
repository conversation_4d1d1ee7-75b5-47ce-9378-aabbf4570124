from datetime import timedelta
from fastapi import APIRouter, Depends
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.session import get_db
from app.models.system.user import User
from app.services.system.auth import (
    authenticate_user,
    create_access_token,
    get_current_user
)
from app.config.settings import settings
from app.domain.response import success, unauthorized
from app.utils.decorators import log_operation

router = APIRouter(prefix="/api/auth", tags=["auth"])

@router.post("/login")
@log_operation("鉴权", "登录")
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
):
    """用户登录获取访问令牌"""
    user = await authenticate_user(db, form_data.username, form_data.password)
    if not user:
        return unauthorized(msg="用户名或密码错误")

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = await create_access_token(
        data={"sub": user.username},
        expires_delta=access_token_expires,
        db=db
    )

    return success({
        "access_token": access_token,
        "token_type": "bearer"
    }, "登录成功")

@router.post("/logout")
@log_operation("鉴权", "登出")
async def logout(
    current_user: User = Depends(get_current_user)
):
    """用户登出"""
    return success("成功登出")

@router.get("/me")
async def read_users_me(
    current_user: User = Depends(get_current_user)
):
    """获取当前用户信息"""
    user_data = {
        "id": current_user.id,
        "username": current_user.username,
        "nickname": current_user.nickname,
        "remark": current_user.remark,
        "status": current_user.status,
        "permissions": getattr(current_user, 'permissions', []),
        "created_at": current_user.created_at.isoformat() if current_user.created_at else None,
        "updated_at": current_user.updated_at.isoformat() if current_user.updated_at else None
    }
    return success(user_data, "获取用户信息成功")