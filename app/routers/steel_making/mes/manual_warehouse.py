from typing import Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.database.session import get_db
from app.models.system.user import User
from app.services.steel_making.mes.manual_warehouse import ManualWarehouseService
from app.services.system.auth import get_current_user
from app.domain.response import success, fail
from app.utils.decorators import log_operation
from app.utils.logger import logger

router = APIRouter(prefix="/api/manual-warehouse")


# 请求模型
class ManualWarehouseRequest(BaseModel):
    materialCode: str = Field(..., description="物料编码")
    warehouseDate: str = Field(..., description="入库日期")
    weight: float = Field(..., gt=0, description="重量")
    factory: str = Field(..., description="工厂")


# 响应模型
class MaterialInfo(BaseModel):
    MATE_CODE: str
    MATE_NAME: str


class ManualWarehouseResponse(BaseModel):
    lesNo: str
    insertSql: str


@router.get("/materials/search")
async def search_materials(
    q: str = Query(..., description="搜索关键词"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """搜索物料信息"""
    try:
        service = ManualWarehouseService(db)
        materials = await service.search_materials(q)
        
        return success(materials)
        
    except Exception as e:
        logger.error(f"搜索物料失败: {e}")
        return fail(f"搜索物料失败: {str(e)}")


@router.get("/materials/all")
async def get_all_materials(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有物料信息"""
    try:
        service = ManualWarehouseService(db)
        materials = await service.get_all_materials()
        
        return success(materials)
        
    except Exception as e:
        logger.error(f"获取所有物料失败: {e}")
        return fail(f"获取所有物料失败: {str(e)}")


@router.get("/reference")
async def get_reference_data(
    materialCode: str = Query(..., description="物料编码"),
    factory: str = Query(..., description="工厂"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取参考数据"""
    try:
        service = ManualWarehouseService(db)
        reference_data = await service.get_reference_data(materialCode, factory)
        
        if reference_data:
            return success(reference_data)
        else:
            return success(None)
        
    except Exception as e:
        logger.error(f"获取参考数据失败: {e}")
        return fail(f"获取参考数据失败: {str(e)}")


@router.post("/create")
@log_operation("MES维护", "人工物料信息入库")
async def create_warehouse_record(
    request: ManualWarehouseRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建入库记录"""
    try:
        service = ManualWarehouseService(db)
        
        # 首先获取物料名称
        materials = await service.search_materials(request.materialCode)
        material_name = ""
        for material in materials:
            if material["MATE_CODE"] == request.materialCode:
                material_name = material["MATE_NAME"]
                break
        
        if not material_name:
            return fail("物料编码不存在")
        
        # 创建入库记录
        result = await service.create_warehouse_record(
            material_code=request.materialCode,
            material_name=material_name,
            weight=request.weight,
            factory=request.factory,
            warehouse_date=request.warehouseDate,
            user_id=current_user.id
        )
        
        return success(result)
        
    except Exception as e:
        logger.error(f"创建入库记录失败: {e}")
        return fail(f"创建入库记录失败: {str(e)}")


@router.get("/history")
async def get_warehouse_history(
    materialCode: Optional[str] = Query(None, description="物料编码"),
    factory: Optional[str] = Query(None, description="工厂"),
    startDate: Optional[str] = Query(None, description="开始日期"),
    endDate: Optional[str] = Query(None, description="结束日期"),
    lesNo: Optional[str] = Query(None, description="LES编号"),
    page: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(20, ge=1, le=100, description="每页大小"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取入库历史记录"""
    try:
        service = ManualWarehouseService(db)
        
        history_data = await service.get_warehouse_history(
            page=page,
            page_size=pageSize,
            material_code=materialCode,
            factory=factory,
            start_date=startDate,
            end_date=endDate,
            les_no=lesNo
        )
        
        return success(history_data)
        
    except Exception as e:
        logger.error(f"获取入库历史失败: {e}")
        return fail(f"获取入库历史失败: {str(e)}")


@router.get("/detail/{record_id}")
async def get_warehouse_detail(
    record_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取入库记录详情"""
    try:
        service = ManualWarehouseService(db)
        
        # 这里可以实现获取单个记录详情的逻辑
        # 暂时返回空，因为前端主要使用历史列表
        return success(None)
        
    except Exception as e:
        logger.error(f"获取入库记录详情失败: {e}")
        return fail(f"获取入库记录详情失败: {str(e)}")


@router.delete("/delete/{record_id}")
@log_operation("MES维护", "人工物料信息入库删除")
async def delete_warehouse_record(
    record_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除入库记录"""
    try:
        service = ManualWarehouseService(db)
        await service.delete_warehouse_record(record_id)
        
        return success(None)
        
    except Exception as e:
        logger.error(f"删除入库记录失败: {e}")
        return fail(f"删除入库记录失败: {str(e)}")
