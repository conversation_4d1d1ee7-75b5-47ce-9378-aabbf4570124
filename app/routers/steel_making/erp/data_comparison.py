from fastapi import APIRouter, Depends, WebSocket, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
from typing import Optional

from app.database.session import get_db
from app.models.common import Factory
from app.services.steel_making.erp.data_comparison import DataComparisonService
from app.services.common.websocket_manager import websocket_manager
from app.services.system.auth import get_current_user
from app.models.system.user import User
from app.domain.response import success, fail
from app.utils.logger import logger
from app.utils.decorators import require_permissions, log_operation
from app.constants.permissions import PERMISSIONS
from app.utils.websocket_utils import generic_websocket_endpoint

router = APIRouter(prefix="/api/data-comparison", tags=["data-comparison"])

class DataComparisonRequest(BaseModel):
    """数据对比请求模型"""
    start_date: str  # 格式: YYYY-MM-DD
    end_date: str    # 格式: YYYY-MM-DD
    factory: str     # 一厂 或 二厂
    heat_no: Optional[str] = None  # 可选：炉号
    material_code: Optional[str] = None  # 可选：物料编码

class TaskStatusResponse(BaseModel):
    """任务状态响应模型"""
    id: int
    task_name: str
    status: str
    progress: int
    total_records: int
    processed_records: int
    difference_count: int
    error_message: Optional[str]
    created_at: Optional[str]
    started_at: Optional[str]
    completed_at: Optional[str]

@router.post("/start")
@require_permissions(PERMISSIONS["STEEL_ERP"]["DATA_COMPARISON"])
@log_operation("ERP数据对比", "启动数据对比任务")
async def start_data_comparison(
    request: DataComparisonRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """启动数据对比任务"""
    try:
        # 验证厂别
        if request.factory not in ["一厂", "二厂"]:
            return fail("厂别参数错误，必须是'一厂'或'二厂'")
        
        factory = Factory.FACTORY_1 if request.factory == "一厂" else Factory.FACTORY_2
        
        # 创建数据对比服务
        service = DataComparisonService(db)
        
        # 创建任务
        task = await service.create_task(
            start_date=request.start_date,
            end_date=request.end_date,
            factory=factory,
            heat_no=request.heat_no,
            material_code=request.material_code,
            user_id=current_user.id
        )
        
        # 定义进度回调函数
        async def progress_callback(task_id: int, progress: int, message: str, data=None):
            await websocket_manager.send_task_progress(
                task_id=task_id,
                progress=progress,
                message=message,
                data=data,  # 传递额外数据
                client_id=str(current_user.id)
            )
        
        # 在后台启动任务
        background_tasks.add_task(
            run_comparison_task,
            task.id,
            db,
            current_user.id,
            progress_callback
        )
        
        return success({
            "task_id": task.id,
            "message": "数据对比任务已启动"
        })
        
    except Exception as e:
        logger.error(f"启动数据对比任务失败: {e}")
        return fail(f"启动任务失败: {str(e)}")

async def run_comparison_task(
    task_id: int, 
    db: AsyncSession, 
    user_id: int,
    progress_callback
):
    """运行数据对比任务的后台函数"""
    try:
        service = DataComparisonService(db)
        await service.start_task(task_id, progress_callback)
        
        # 发送完成通知
        await websocket_manager.send_task_completed(
            task_id=task_id,
            success=True,
            result_summary="数据对比任务执行成功",
            client_id=str(user_id)
        )
        
    except Exception as e:
        logger.error(f"后台任务执行失败: {e}")
        
        # 发送失败通知
        await websocket_manager.send_task_completed(
            task_id=task_id,
            success=False,
            result_summary=f"任务执行失败: {str(e)}",
            client_id=str(user_id)
        )

@router.get("/task/{task_id}/status")
async def get_task_status(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务状态"""
    try:
        service = DataComparisonService(db)
        status = await service.get_task_status(task_id)
        
        if not status:
            return fail("任务不存在")
        
        return success(status)
        
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        return fail(f"获取任务状态失败: {str(e)}")


@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """实时通信"""
    await generic_websocket_endpoint(websocket, client_id, "数据对比")
