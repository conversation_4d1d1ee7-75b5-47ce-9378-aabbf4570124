from fastapi import HTTPException, status, Request
from fastapi.responses import JSO<PERSON>esponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from typing import Union
import traceback
from app.utils.logger import logger


class APIException(HTTPException):
    """基础API异常类"""
    def __init__(
        self,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        detail: str = None,
        code: int = None,
    ):
        super().__init__(status_code=status_code, detail=detail)
        self.code = code if code is not None else status_code


class BusinessException(APIException):
    """业务异常基类"""
    def __init__(self, detail: str, code: int = None):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail,
            code=code or 4000
        )


class ValidationException(APIException):
    """参数验证异常"""
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail,
            code=4220
        )


class AuthenticationException(APIException):
    """认证异常"""
    def __init__(self, detail: str = "认证失败"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            code=4010
        )


class AuthorizationException(APIException):
    """授权异常"""
    def __init__(self, detail: str = "权限不足"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            code=4030
        )


class ResourceNotFoundException(APIException):
    """资源不存在异常"""
    def __init__(self, detail: str = "资源不存在"):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            code=4040
        )


class DatabaseException(APIException):
    """数据库异常"""
    def __init__(self, detail: str = "数据库操作失败"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            code=5001
        )


class ExternalServiceException(APIException):
    """外部服务异常"""
    def __init__(self, detail: str = "外部服务调用失败"):
        super().__init__(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=detail,
            code=5020
        )


# 兼容旧的异常类
class UserAlreadyExists(BusinessException):
    """用户已存在异常"""
    def __init__(self, detail: str = "用户已存在"):
        super().__init__(detail=detail, code=1001)


class UserNotFound(ResourceNotFoundException):
    """用户不存在异常"""
    def __init__(self, detail: str = "用户不存在"):
        super().__init__(detail=detail)
        self.code = 1002


class InvalidCredentials(AuthenticationException):
    """无效凭证异常"""
    def __init__(self, detail: str = "用户名或密码错误"):
        super().__init__(detail=detail)
        self.code = 1003


# 全局异常处理器
async def api_exception_handler(request: Request, exc: APIException) -> JSONResponse:
    """API异常处理器"""
    logger.warning(f"API异常: {exc.detail} - 状态码: {exc.status_code} - 错误码: {exc.code}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.code,
            "msg": exc.detail,
            "data": None
        }
    )


async def http_exception_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
    """HTTP异常处理器"""
    logger.warning(f"HTTP异常: {exc.detail} - 状态码: {exc.status_code}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.status_code,
            "msg": exc.detail or "请求处理失败",
            "data": None
        }
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """参数验证异常处理器"""
    error_details = []
    for error in exc.errors():
        field = " -> ".join(str(loc) for loc in error["loc"])
        message = error["msg"]
        error_details.append(f"{field}: {message}")

    detail = "参数验证失败: " + "; ".join(error_details)
    logger.warning(f"参数验证异常: {detail}")

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "code": 4220,
            "msg": detail,
            "data": None
        }
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}")
    logger.error(f"异常堆栈: {traceback.format_exc()}")

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "code": 5000,
            "msg": "服务器内部错误",
            "data": None
        }
    )